import { useThrottleFn } from 'ahooks'
import { useCallback, useRef } from 'react'

export function useAutoScroll<T extends HTMLElement>() {
  const domRef = useRef<T>(null)
  const scrollCacheRef = useRef({
    needAutoScroll: true,
    prevScrollTop: 0,
  })

  // 滚动到底部
  const { run: scrollToBottom } = useThrottleFn(() => {
    const dom = domRef.current
    if (dom) {
      dom.scrollTo({
        top: dom.scrollHeight,
        behavior: 'smooth',
      })
    }
    scrollCacheRef.current.needAutoScroll = true
  }, { wait: 300 })

  // 监听滚动事件
  const onScroll = useCallback((e: React.UIEvent<T>) => {
    const target = e.currentTarget
    // 判断是否滚动到底部（允许有2像素误差）
    const isAtBottom = Math.abs(target.scrollHeight - target.scrollTop - target.clientHeight) < 1

    if (isAtBottom) {
      scrollCacheRef.current.needAutoScroll = true
    }
    else if (target.scrollTop < scrollCacheRef.current.prevScrollTop) {
      // 向上滚动，暂停自动滚动
      scrollCacheRef.current.needAutoScroll = false
    }
    scrollCacheRef.current.prevScrollTop = target.scrollTop
  }, [])

  // 内容变化时自动滚动
  const tryAutoScroll = useCallback(() => {
    if (scrollCacheRef.current.needAutoScroll) {
      scrollToBottom()
    }
  }, [scrollToBottom])

  // 手动恢复自动滚动
  const resumeAutoScroll = useCallback(() => {
    scrollCacheRef.current.needAutoScroll = true
    scrollToBottom()
  }, [scrollToBottom])

  return {
    domRef,
    onScroll,
    tryAutoScroll,
    resumeAutoScroll,
  }
}
