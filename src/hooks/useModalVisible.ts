import { useMemoizedFn } from 'ahooks'
import { nanoid } from 'nanoid'
import { useState } from 'react'

interface State<T = any> {
  selectItem?: T
  visible: boolean
  key: string
}

function useModalVisible<T = any>() {
  const [state, setState] = useState<State<T>>({
    selectItem: undefined,
    visible: false,
    key: nanoid(),
  })

  const setTrue = useMemoizedFn((info?: T) => {
    setState({
      visible: true,
      key: nanoid(),
      selectItem: info,
    })
  })

  const setFalse = useMemoizedFn(() => {
    setState({
      visible: false,
      key: nanoid(),
      selectItem: undefined,
    })
  })

  return {
    ...state,
    action: {
      setTrue,
      setFalse,
    },
  }
}
export default useModalVisible
