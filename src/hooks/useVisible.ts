import type { Actions } from 'ahooks/lib/useBoolean'
import { useBoolean, useMemoizedFn } from 'ahooks'
import { useState } from 'react'

function useVisible(defaultValue?: boolean): [
  boolean,
  Actions & {
    visibleKey: number
  },
] {
  const [visible, actions] = useBoolean(defaultValue)
  const [visibleKey, setVisibleKey] = useState(1)

  const setTrue = useMemoizedFn(() => {
    setVisibleKey(key => key + 1)
    actions.setTrue()
  })

  return [
    visible,
    {
      ...actions,
      setTrue,
      visibleKey,
    },
  ]
}
export default useVisible
