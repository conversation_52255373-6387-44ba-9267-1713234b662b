export enum RoleEnum {
  /**
   * 用户
   */
  USER = 'user',

  /**
   * 助手
   */
  ASSISTANT = 'assistant',
}

export enum TimeTypeEnum {
  /**
   * 区间
   */
  Range = 'range',

  /**
   * 单个时间
   */
  Point = 'point',
}

export enum ComponentTimeTypeEnum {
  /**
   * 区间
   */
  Range = '0',

  /**
   * 单个时间
   */
  Point = '1',
}

// 接口用的
export enum ComponentTimeGranularityEnum {
  /**
   * 小时
   */
  Hour = 'hour',

  /**
   * 天
   */
  Day = 'day',

  /**
   * 月
   */
  Month = 'month',

  /**
   * 年
   */
  Year = 'year',
}

// antd选择器用的
export enum TimeUnitEnum {
  /**
   * 小时
   */
  Hour = 'hour',

  /**
   * 天
   */
  Day = 'date',

  /**
   * 月
   */
  Month = 'month',

  /**
   * 年
   */
  Year = 'year',
}

export enum ComponentTypeEnum {
  /**
   * echarts
   */
  Echarts = 'ECharts',

  /**
   * 排名
   */
  TopN = 'TopN',

  /**
   * 地图
   */
  Map = 'Map',
}

export enum GridColSpanEnum {
  /**
   * 1
   */
  Col1 = 'col-span-1',

  /**
   * 2
   */
  Col2 = 'col-span-2',

  /**
   * 3
   */
  Col3 = 'col-span-3',

  /**
   * 4
   */
  Col4 = 'col-span-4',
}

// 组件时间粒度枚举
export enum ComponentTimeGranularityProEnum {
  /**
   * 今日
   */
  Today = 'today',

  /**
   * 昨日
   */
  Yesterday = 'yesterday',

  /**
   * 本周
   */
  ThisWeek = 'this_week',

  /**
   * 上周
   */
  LastWeek = 'last_week',

  /**
   * 本月
   */
  ThisMonth = 'this_month',

  /**
   * 上月
   */
  LastMonth = 'last_month',

  /**
   * 今年
   */
  ThisYear = 'this_year',

  /**
   * 去年
   */
  LastYear = 'last_year',

  /**
   * 近三月
   */
  LastThreeMonths = 'last_three_months',

  /**
   * 近六个月
   */
  LastSixMonths = 'last_six_months',

  /**
   * 近三年
   */
  LastThreeYears = 'last_three_years',

  /**
   * 近1小时
   */
  LastOneHour = 'last_one_hour',

  /**
   * 近24小时
   */
  Last24Hours = 'last_24_hours',
}
