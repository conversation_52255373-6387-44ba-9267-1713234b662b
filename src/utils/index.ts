import { createBrowserHistory } from 'history'

export const history = createBrowserHistory()

/**
 * 获取表格列
 * @param data 数据
 * @returns 表格列
 */
export function getColumns(data: Record<string, string>[]) {
  if (!data)
    return []
  const info = data?.[0]
  if (!info)
    return []
  return Object.keys(info).map((item) => {
    return {
      title: item,
      dataIndex: item,
    }
  })
}

/**
 * 安全地解析 JSON 字符串
 * @param {string} jsonString - 要解析的 JSON 字符串
 * @param {*} [defaultValue] - 解析失败时返回的默认值
 * @returns {*} 解析后的对象，或默认值
 */
export function safeJsonParse(jsonString?: string, defaultValue?: any) {
  if (!jsonString)
    return defaultValue
  try {
    return JSON.parse(jsonString)
  }
  catch (error: any) {
    console.warn('JSON 解析失败:', error.message)
    return defaultValue
  }
}

/**
 * 判断json字符串是否为空 | "[]" | "{}" | "null" | "undefined"
 * @param jsonString 字符串
 * @returns 是否为空
 */
export function judgeEmptyJson(jsonString?: string) {
  if (!jsonString || jsonString === '[]' || jsonString === '{}' || jsonString === 'null' || jsonString === 'undefined')
    return false
  return true
}
