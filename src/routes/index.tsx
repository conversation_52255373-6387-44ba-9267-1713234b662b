import AuthRoute from '@/components/AuthRoute'
import Layout from '@/layout'
import BaseLayout from '@/layout/BaseLayout'
import { lazy } from 'react'
import { createBrowserRouter } from 'react-router-dom'

const routes = createBrowserRouter([
  {
    path: '/',
    element: (
      <AuthRoute>
        <BaseLayout />
      </AuthRoute>
    ),
    children: [
      {
        path: '/login',
        Component: lazy(() => import('@/pages/Login')),
      },
      {
        path: '/dataBoardEdit/:id',
        Component: lazy(() => import('@/pages/DataBoardEdit')),
      },
      {
        path: '/',
        element: (
          <Layout />
        ),
        children: [
          {
            path: '/home',
            Component: lazy(() => import('@/pages/Home')),
          },
          {
            path: '/history',
            Component: lazy(() => import('@/pages/History')),
          },
          {
            path: '/dataBoardList',
            Component: lazy(() => import('@/pages/DataBoardList')),
          },
          {
            path: '/dataPanelList',
            Component: lazy(() => import('@/pages/DataPanelList')),
          },
        ],
      },
    ],
  },
])

export default routes
