import useStore from '@/store'
import { useMount } from 'ahooks'
import { ConfigProvider, message } from 'antd'
import locale from 'antd/es/locale/zh_CN'
import { Outlet } from 'react-router-dom'

function BaseLayout() {
  const [messageApi, contextHolder] = message.useMessage()

  useMount(() => {
    useStore.setState((draft) => {
      draft.messageApi = messageApi
    })
  })

  return (
    <ConfigProvider
      locale={locale}
      theme={{
        token: {
          colorPrimary: '#203FDF',
        },
        components: {
          Card: {
            headerHeight: 48,
            bodyPadding: 16,
          },
        },
      }}
    >
      <Outlet />
      {contextHolder}
    </ConfigProvider>
  )
}

export default BaseLayout
