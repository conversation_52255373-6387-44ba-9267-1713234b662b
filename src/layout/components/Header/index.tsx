import useStore from '@/store'
import { DesktopOutlined } from '@ant-design/icons'
import { Avatar, Button, Dropdown } from 'antd'
import { useNavigate } from 'react-router-dom'

function Header() {
  const userInfo = useStore(state => state.userInfo)
  const onToLogin = useStore(state => state.onToLogin)
  const navigate = useNavigate()

  const items = [
    {
      key: 'history',
      label: '历史会话',
      onClick: () => {
        navigate('/history')
      },
    },
    {
      key: 'settings',
      label: '设置',
      disabled: true,
      onClick: () => {
        navigate('/settings')
      },
    },
    {
      key: 'logout',
      label: '退出登录',
      danger: true,
      onClick: () => {
        onToLogin()
      },
    },
  ]
  return (
    <div className="h-[64px] min-h-[64px] w-full bg-white/90 backdrop-blur-md z-50 border-b border-gray-100 shadow-xs max-2xl:h-[51px] max-2xl:min-h-[51px]">
      <nav className="w-full h-full flex items-center justify-between px-4 sm:px-6">
        <div
          className="text-lg font-bold cursor-pointer"
          onClick={() => {
            navigate('/')
          }}
        >
          和信 ChatBI
        </div>
        <div className="flex gap-[16px]">
          <div className="text-[14px]">
            <Button
              icon={<DesktopOutlined />}
              onClick={() => {
                navigate('/dataBoardList')
              }}
            >
              数据看板
            </Button>
          </div>
          <Dropdown menu={{ items }}>
            <div
              className="flex items-center gap-2 cursor-pointer"
              onClick={(e) => {
                e.stopPropagation()
              }}
            >
              <Avatar />
              <span className="text-[14px]">
                您好，
                {userInfo?.username}
              </span>
            </div>
          </Dropdown>
        </div>
      </nav>
    </div>
  )
}

export default Header
