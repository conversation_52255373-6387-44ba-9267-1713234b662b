import useStore from '@/store'
import { useMount } from 'ahooks'
import dayjs from 'dayjs'
import Content from './components/Content'
import Header from './components/Header'
import { browserLangMap } from './constant'
import 'dayjs/locale/zh-cn'

dayjs.locale('zh-cn')

function Layout() {
  const { getUserInfo } = useStore()
  useMount(() => {
    // 刷新用户信息
    getUserInfo()
    const browserLang = navigator.language
    const _lang = browserLangMap[browserLang as keyof typeof browserLangMap]
    const lang = window.localStorage.getItem('lang')
    if (!lang) {
      window.localStorage.setItem('lang', _lang)
      window.location.reload()
    }
  })
  return (
    <main className="size-full flex flex-col overflow-hidden bg-[#F8F8FA]">
      <Header />
      <Content />
    </main>
  )
}

export default Layout
