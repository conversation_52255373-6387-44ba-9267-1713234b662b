import type { MessageInstance } from 'antd/es/message/interface'
import userService from '@/services/userService'
import { createJSONStorage, persist } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'
import { shallow } from 'zustand/shallow'
import { createWithEqualityFn } from 'zustand/traditional'

interface Store {
  token?: string
  token_type?: string
  userInfo?: API.User.DetailResult
  setToken: (token: Store['token']) => void
  getToken: () => Store['token']
  setTokenType: (token_type: Store['token_type']) => void
  getTokenType: () => Store['token_type']
  getUserInfo: (callback?: () => void) => Promise<void>
  onToLogin: () => void
  messageApi: MessageInstance
}

const useStore = createWithEqualityFn<Store>()(
  persist(
    immer((set, get) => ({
      token: undefined,
      token_type: undefined,
      userInfo: undefined,
      setToken: (token: Store['token']) => set({ token }),
      setTokenType: (token_type: Store['token_type']) => set({ token_type }),
      getToken: () => get().token,
      getTokenType: () => get().token_type,
      getUserInfo: async (callback?: () => void) => {
        const res = await userService.detail()
        set({ userInfo: res })
        callback?.()
      },
      onToLogin: () => {
        set({ token: undefined, token_type: undefined, userInfo: undefined })
        window.location.href = '/login'
      },
      messageApi: undefined!,
    })),
    {
      name: 'store',
      storage: createJSONStorage(() => localStorage),
    },
  ),
  shallow,
)

export default useStore
