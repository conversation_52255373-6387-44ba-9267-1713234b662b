import type { GridColSpanEnum, TimeUnitEnum } from '@/enum'
import type { FormInstance, RadioChangeEvent } from 'antd'
import BOTTOMIMAGE from '@/assets/images/bottom.png'
import EDITIMAGE from '@/assets/images/edit.png'
import TOPPOSTION from '@/assets/images/top-position.png'
import TOPIMAGE from '@/assets/images/top.png'
import { dictInfo } from '@/dict'
import { ComponentTimeTypeEnum, ComponentTypeEnum } from '@/enum'
import MapComp from '@/pages/Home/components/Content/components/BubbleItem/components/Map'
import TopNComp from '@/pages/Home/components/Content/components/BubbleItem/components/TopN'
import VisualizationComp from '@/pages/Home/components/Content/components/BubbleItem/components/Visualization'
import homeService from '@/services/homeService'
import { DeleteOutlined, FullscreenExitOutlined, FullscreenOutlined } from '@ant-design/icons'
import { But<PERSON>, DatePicker, Form, message, Modal, Radio, Row, Tooltip } from 'antd'
import dayjs from 'dayjs'
import React, { useCallback, useMemo, useRef, useState } from 'react'
import ReactCustomScrollbars from 'react-custom-scrollbars'

interface IProps {
  item: API.Home.GetBoardListResultItem
  isGrid?: boolean
  disabledMoveUp?: boolean
  disabledMoveDown?: boolean
  className?: string
  onEdit: () => void
  onMoveUp: () => void
  onMoveDown: () => void
  onSearch: (start_time: string, stop_time: string) => void
  onScale?: (value: GridColSpanEnum) => void
  onRefresh: () => void
}

export interface FormRefType {
  unit: TimeUnitEnum
  time: dayjs.Dayjs
  timeRang: dayjs.Dayjs[]
}

function PanelItem(props: IProps) {
  const { item, isGrid = false, disabledMoveUp = false, disabledMoveDown = false, className, onRefresh, onEdit, onMoveUp, onMoveDown, onScale, onSearch } = props

  const formRef = useRef<FormInstance<FormRefType>>(null)
  const fullscreenRef = useRef<HTMLDivElement>(null)

  const [isFullscreen, setIsFullscreen] = useState(false)

  const getFormatFormValue = useCallback(() => {
    const formValues = formRef.current?.getFieldsValue()
    if (formValues) {
      return {
        start_time: item.time_type === ComponentTimeTypeEnum.Point ? '' : formValues?.timeRang[0]?.format('YYYY-MM-DD HH:mm:ss'),
        stop_time: item.time_type === ComponentTimeTypeEnum.Point ? formValues?.time?.format('YYYY-MM-DD HH:mm:ss') : formValues?.timeRang[1]?.format('YYYY-MM-DD HH:mm:ss'),
      }
    }
    return false
  }, [item.time_type])

  const onHandleQuery = () => {
    const values = getFormatFormValue()
    if (!values) {
      return
    }
    onSearch?.(values.start_time, values.stop_time)
  }

  const topText = useMemo(() => {
    return item.is_topping === '1' ? '取消置顶' : '置顶'
  }, [item.is_topping])

  const onHandleDelete = () => {
    Modal.confirm({
      title: '确定删除该组件吗？',
      onOk: async () => {
        await homeService.deleteComponent(item.id)
        message.success('删除成功')
        onRefresh()
      },
    })
  }

  const onHandleTopping = () => {
    const isTopping = item.is_topping === '1'
    homeService.toppingComponent({
      component_id: item.id,
      topping_status: isTopping ? '0' : '1',
    })
    message.success(isTopping ? '取消置顶成功' : '置顶成功')
    onRefresh()
  }

  // 上移和下移采用本地处理，静默请求接口
  const onHandleMoveUp = () => {
    homeService.moveUpComponent(item.id)
    onMoveUp()
  }

  const onHandleMoveDown = () => {
    homeService.moveDownComponent(item.id)
    onMoveDown()
  }

  const onHandleGridColSpanChange = (e: RadioChangeEvent) => {
    const value = e.target.value
    onScale?.(value)
  }

  const onHandleFullscreen = () => {
    // 全屏展示
    const dom = fullscreenRef.current
    if (isFullscreen) {
      document.exitFullscreen()
      setIsFullscreen(false)
    }
    else {
      dom?.requestFullscreen()
      setIsFullscreen(true)
    }
  }

  return (
    <React.Fragment>
      <div key={item.id} ref={fullscreenRef} className={`h-[360px] w-full flex gap-[6px] flex-col cursor-pointer px-[8px] py-[34px] pb-[8px] border border-[#d4d4d4] rounded-[8px] relative bg-white ${className}`}>
        {item.is_topping === '1' && (
          <div className="absolute top-[4px] left-[4px] bg-[#60AD29] text-white text-[12px] px-[8px] py-[2px] rounded-[4px]">
            置顶
          </div>
        )}
        {
          isGrid && !isFullscreen && (
            <div className="absolute top-[4px] right-[38px]">
              <Radio.Group block optionType="button" options={dictInfo.gridColSpan} value={item.json} onChange={onHandleGridColSpanChange} />
            </div>
          )
        }
        <div className="size-[32px] absolute top-[4px] right-[4px] flex justify-center items-center hover:bg-[#E5E5E5] rounded-[4px] p-[4px]" onClick={onHandleFullscreen}>
          {isFullscreen
            ? <FullscreenExitOutlined className="text-[18px] cursor-pointer" />
            : <FullscreenOutlined className="text-[18px] cursor-pointer" />}
        </div>
        <div className="w-full text-[16px] font-bold text-center">
          {item.name}
        </div>
        <div className="flex items-center justify-center">
          <Form
            ref={formRef}
            layout="inline"
            initialValues={{
              time: dayjs(),
              timeRang: [dayjs(item?.start_time), dayjs(item?.stop_time)],
            }}
          >
            <React.Fragment>
              {
                item?.time_type === ComponentTimeTypeEnum.Point && (
                  <Row>
                    <Form.Item name="time">
                      <DatePicker
                        showTime
                        className="w-[230px]"
                        format="YYYY-MM-DD HH:mm:ss"
                      />
                    </Form.Item>
                  </Row>
                )
              }
              {
                item?.time_type === ComponentTimeTypeEnum.Range && (
                  <Form.Item name="timeRang">
                    <DatePicker.RangePicker
                      showTime
                      className="w-[230px]"
                      format="YYYY-MM-DD HH:mm:ss"
                    />
                  </Form.Item>
                )
              }
            </React.Fragment>
          </Form>
          <React.Fragment>
            <Button
              type="primary"
              onClick={() => onHandleQuery()}
            >
              查询
            </Button>
          </React.Fragment>
        </div>
        <div className="flex-1 overflow-hidden">
          <ReactCustomScrollbars className="size-full">
            {
              item.component_type === ComponentTypeEnum.TopN && <TopNComp value={item.code} toolbar={false} />
            }
            {
              item.component_type === ComponentTypeEnum.Echarts && <VisualizationComp value={item.code} toolbar={false} />
            }
            {
              item.component_type === ComponentTypeEnum.Map && <MapComp value={item.code} toolbar={false} />
            }
          </ReactCustomScrollbars>
        </div>
        <div className="h-[32px] flex gap-[6px] justify-end items-center">
          {!disabledMoveUp && (
            <Tooltip title="上移">
              <div className="size-[24px] cursor-pointer flex justify-center items-center hover:bg-[#E5E5E5] rounded-[4px] p-[4px]" onClick={onHandleMoveUp}>
                <img src={TOPIMAGE} alt="" className="size-full" />
              </div>
            </Tooltip>
          )}
          {!disabledMoveDown && (
            <Tooltip title="下移">
              <div className="size-[24px] cursor-pointer flex justify-center items-center hover:bg-[#E5E5E5] rounded-[4px] p-[4px]" onClick={onHandleMoveDown}>
                <img src={BOTTOMIMAGE} alt="" className="size-full" />
              </div>
            </Tooltip>
          )}
          <Tooltip title="编辑">
            <div className="size-[24px] cursor-pointer flex justify-center items-center hover:bg-[#E5E5E5] rounded-[4px] p-[4px]" onClick={() => onEdit()}>
              <img src={EDITIMAGE} alt="" className="size-full" />
            </div>
          </Tooltip>
          <Tooltip title={topText}>
            <div className="size-[24px] cursor-pointer flex justify-center items-center hover:bg-[#E5E5E5] rounded-[4px] p-[4px]" onClick={onHandleTopping}>
              <img src={TOPPOSTION} alt="" className="size-full" />
            </div>
          </Tooltip>
          <Tooltip title="删除">
            <div className="size-[24px] cursor-pointer flex justify-center items-center hover:bg-[#E5E5E5] rounded-[4px] p-[4px]" onClick={onHandleDelete}>
              <DeleteOutlined style={{ fontSize: '14px', color: '#FF414B' }} />
            </div>
          </Tooltip>
        </div>
      </div>
    </React.Fragment>
  )
}

export default PanelItem
