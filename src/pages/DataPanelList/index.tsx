import { ComponentTypeEnum, type GridColSpanEnum } from '@/enum'
import type { SaveComponentInfo } from '../Home/container'
import useModalVisible from '@/hooks/useModalVisible'
import homeService from '@/services/homeService'
import { LeftOutlined } from '@ant-design/icons'
import { useMount, useRequest, useSetState } from 'ahooks'
import { Empty, Input, Skeleton } from 'antd'
import { arrayMoveImmutable } from 'array-move'
import React, { useMemo, useState } from 'react'
import InfiniteScroll from 'react-infinite-scroll-component'
import SaveCompBoard from '../Home/components/SaveCompBoard'
import PanelItem from './components/PanelItem'

interface IPageInfoType {
  page: number
  size: number
  total: number
}

function DataPanelList() {
  const [list, setList] = useState<API.Home.GetBoardListResultItem[]>([])

  const { visible, key, action, selectItem } = useModalVisible()

  const [pageInfo, setPageInfo] = useSetState<IPageInfoType>({
    page: 1,
    size: 10,
    total: 0,
  })

  const [hasMore, setHasMore] = useState(true)

  const { run: requestList, loading } = useRequest(async ({ page, size }: { page: number, size: number }) => await homeService.getBoardList({ page, size }), {
    onSuccess: (res: API.Home.GetBoardListResult) => {
      const hasMore = res.pagination.total > list.length + res.components.length
      setHasMore(hasMore)
      setList((prev) => {
        if (res.pagination.page === 1) {
          return res.components
        }
        return [...prev, ...res.components]
      })
      setPageInfo({
        page: res.pagination.page,
        size: res.pagination.size,
        total: res.pagination.total,
      })
    },
    manual: true,
  })

  // 置顶的组件数量
  const toppingCount = useMemo(() => {
    return list.filter(item => item.is_topping === '1').length
  }, [list])

  useMount(() => {
    requestList({
      page: pageInfo.page,
      size: pageInfo.size,
    })
  })

  const onRefresh = () => {
    requestList({
      page: 1,
      size: pageInfo.size,
    })
  }

  const onEdit = (item: API.Home.GetBoardListResultItem) => {
    action.setTrue({
      component_name: item.name,
      component_id: item.id,
      workflow_id: item.workflow_id,
      component_info: {
        code: item.code,
        json: item.json,
        start_time: item.start_time,
        stop_time: item.stop_time,
        time_type: item.time_type,
        component_type: item.component_type,
      },
    } as SaveComponentInfo)
  }

  const onMoveUp = (index: number) => {
    const newList = arrayMoveImmutable(list, index, index - 1)
    setList(newList)
  }

  const onMoveDown = (index: number) => {
    const newList = arrayMoveImmutable(list, index, index + 1)
    setList(newList)
  }

  const onScale = async (item: API.Home.GetBoardListResultItem, value: GridColSpanEnum) => {
    const params = {
      component_name: item.name,
      component_id: item.id,
      workflow_id: item.workflow_id,
      component_info: {
        code: item.code,
        json: value,
        component_type: item.component_type,
        start_time: item.start_time,
        stop_time: item.stop_time,
        time_type: item.time_type,
      },
    }
    await homeService.saveComponent(params)
    setList((prev) => {
      return prev.map((item) => {
        if (item.id === params.component_id) {
          return {
            ...item,
            json: value,
          }
        }
        return item
      })
    })
  }

  const onSearch = async (item: API.Home.GetBoardListResultItem, start_time: string, stop_time: string) => {
    const params = {
      workflow_id: item.workflow_id,
      new_time_end: stop_time,
      new_time_start: start_time,
    }
    const res = await homeService.getTimeSelect(params)
    setList((prev) => {
      return prev.map((draft) => {
        if (draft.id === item.id) {
          const code = item.component_type === ComponentTypeEnum.Echarts ? res.visualization_data : item.component_type === ComponentTypeEnum.Map ? res.map : res.topN
          return { ...draft, code: code ?? draft.code }
        }
        return draft
      })
    })
  }

  return (
    <React.Fragment>
      <div className="flex flex-col h-full bg-white">
        <div className="h-[48px] flex items-center px-[16px] border-b border-solid border-[#E5E5E5]">
          <div className="w-[300px] flex gap-[6px] items-center justify-start">
            <LeftOutlined style={{ fontSize: '12px', color: '#999' }} />
            <span className="text-[#999] text-[14px] cursor-pointer hover:text-[#333]">返回</span>
          </div>
          <div className="flex-1 text-center text-[16px] font-bold">看板列表</div>
          <Input.Search
            placeholder="请输入看板名称"
            style={{
              width: '300px',
            }}
          />
        </div>
        <div className="flex-1 overflow-hidden">
          <div className="size-full overflow-auto" id="scrollableDiv">
            <InfiniteScroll
              dataLength={list.length}
              next={() => {
                requestList({
                  page: pageInfo.page + 1,
                  size: pageInfo.size,
                })
              }}
              scrollableTarget="scrollableDiv"
              hasMore={hasMore}
              loader={<div className="text-center py-2"><Skeleton paragraph={{ rows: 1 }} active /></div>}
              className="!overflow-visible"
            >
              <div className="grid grid-cols-4 gap-[12px] p-[16px]">
                {
                  list.map((item, index) => (
                  // 非置顶第一个组件没有上移
                    <PanelItem
                      key={item.id}
                      item={item}
                      isGrid
                      disabledMoveUp={index === 0 || index === toppingCount}
                      disabledMoveDown={index === list.length - 1 || index === toppingCount - 1}
                      onRefresh={onRefresh}
                      onMoveUp={() => onMoveUp(index)}
                      onMoveDown={() => onMoveDown(index)}
                      onEdit={() => {
                        onEdit(item)
                      }}
                      onSearch={(start_time, stop_time) => {
                        onSearch(item, start_time, stop_time)
                      }}
                      onScale={(value) => {
                        onScale(item, value)
                      }}
                      className={item.json}
                    />
                  ))
                }
              </div>
              {
                list.length === 0 && !loading && (
                  <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                )
              }
              {
                loading && list.length === 0 && (
                  <div className="flex justify-center items-center h-full">
                    <Skeleton />
                  </div>
                )
              }
            </InfiniteScroll>
          </div>
        </div>
        <SaveCompBoard
          key={key}
          open={visible}
          onCancel={() => {
            action.setFalse()
            onRefresh()
          }}
          selectItem={selectItem}
        />
      </div>
    </React.Fragment>
  )
}

export default DataPanelList
