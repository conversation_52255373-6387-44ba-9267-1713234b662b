import homeService from '@/services/homeService'
import { DeleteOutlined, SearchOutlined } from '@ant-design/icons'
import { useMount, useRequest, useSetState } from 'ahooks'
import { Button, Empty, Input, message, Modal, Skeleton, Tooltip } from 'antd'
import dayjs from 'dayjs'
import React, { useCallback, useState } from 'react'
import InfiniteScroll from 'react-infinite-scroll-component'
import styles from './index.module.less'
import { useNavigate } from 'react-router-dom'

interface IPageInfoType {
  page: number
  size: number
  total: number
}

function History() {
  const [list, setList] = useState<API.Home.ConversationsItem[]>([])
  const navigate = useNavigate()
  const [pageInfo, setPageInfo] = useSetState<IPageInfoType>({
    page: 1,
    size: 10,
    total: 0,
  })

  const [hasMore, setHasMore] = useState(true)

  const { run: requestList, loading } = useRequest(async ({ page, size }: { page: number, size: number }) => await homeService.getConversations({
    page,
    size,
  }), {
    onSuccess: (res) => {
      setList(prev => {
        if (pageInfo.page === 1) {
          return res.data
        }
        return [...prev, ...res.data]
      })
      setHasMore(res.page_info.has_more)
      setPageInfo({
        page: res.page_info.page,
        size: res.page_info.size,
        total: res.page_info.total,
      })
    },
    manual: true,
  })

  const deleteConversation = useCallback(async (id: string) => {
    Modal.confirm({
      title: '提示',
      content: '确定删除该会话吗？',
      onOk: async () => {
        await homeService.deleteConversation(String(id))
        setList(prev => prev.filter(item => item.id !== id))
        message.success('删除成功')
      },
    })
  }, [])

  const onHandleDeleteAllConversations = useCallback(async () => {
    Modal.confirm({
      title: '提示',
      content: '确定要删除所有会话吗？',
      onOk: async () => {
        await homeService.deleteAllConversations()
        message.success('删除成功')
        setList([])
        setPageInfo({
          page: 1,
          size: 10,
          total: 0,
        })
      },
    })
  }, [setPageInfo])

  const onHandleClickConversation = useCallback((id: string) => {
    navigate(`/home?conversationId=${id}`)
  }, [])

  useMount(() => {
    requestList({
      page: pageInfo.page,
      size: pageInfo.size,
    })
  })

  return (
    <React.Fragment>
      <div className={`w-full h-full flex justify-center items-center ${styles.main}`}>
        <div className="!w-[60%] h-full overflow-auto relative" id="scrollableDiv">
          <InfiniteScroll
            dataLength={list.length}
            next={() => {
              requestList({
                page: pageInfo.page + 1,
                size: pageInfo.size,
              })
            }}
            scrollableTarget="scrollableDiv"
            hasMore={hasMore}
            endMessage={false}
            loader={false}
            className="!overflow-visible"
          >
            <div className="bg-white rounded-[12px] flex flex-col !p-[16px] !max-2xl:p-[12px] min-h-[calc(100vh-63px-21px-22px)] max-2xl:min-h-[calc(100vh-51px-16px-17px)]">
              <div className="text-[18px] max-2xl:text-[16px] font-bold py-[10px] flex items-center justify-between">
                历史会话
                <Button
                  type="primary"
                  onClick={() => {
                    onHandleDeleteAllConversations()
                  }}
                >
                  清空会话
                </Button>
              </div>
              <div className="sticky top-0 z-10 bg-white py-[16px]">
                <Input
                  placeholder="搜索历史会话"
                  style={{
                    height: '40px',
                  }}
                  prefix={(
                    <SearchOutlined style={{
                      fontSize: '18px',
                    }}
                    />
                  )}
                />
              </div>
              <div className="flex flex-col gap-[12px] mt-[16px]">
                {
                  list.map(item => (
                    <div key={item.id} className="min-h-[120px] flex flex-col gap-[6px] hover:bg-[#F5F5F5] cursor-pointer p-[8px] rounded-[8px]" onClick={() => onHandleClickConversation(item.id)}>
                      <div className="text-[18px] max-2xl:text-[16px] font-bold ">
                        {item.name}
                      </div>
                      <div className="flex-1 text-[14px] max-2xl:text-[12px] text-[#999] line-clamp-2">
                        {item.summary ?? '--'}
                      </div>
                      <div className="flex justify-between items-center">
                        <div className="text-[12px] text-[#999]">
                          {dayjs(item.updated_at).format('YYYY-MM-DD HH:mm:ss')}
                        </div>
                        <div
                          className="cursor-pointer size-[24px] hover:bg-[#b4b1b1] flex justify-center items-center rounded-[4px]"
                          onClick={(e) => {
                            e.stopPropagation()
                            deleteConversation(item.id)
                          }}
                        >
                          <Tooltip title="删除会话">
                            <DeleteOutlined style={{
                              color: '#ff4d4e',
                            }}
                            />
                          </Tooltip>
                        </div>
                      </div>
                    </div>
                  ))
                }
                {
                  list.length === 0 && !loading && (
                    <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                  )
                }
                {
                  loading && list.length === 0 && (
                    <div className="flex justify-center items-center h-full">
                      <Skeleton />
                    </div>
                  )
                }
              </div>
            </div>
          </InfiniteScroll>
        </div>
      </div>
    </React.Fragment>
  )
}

export default History
