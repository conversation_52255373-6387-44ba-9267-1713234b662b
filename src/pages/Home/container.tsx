import type { ThoughtChainItem } from '@ant-design/x'
import type { ConversationDataItem } from './hooks/typings'
import useModalVisible from '@/hooks/useModalVisible'
import homeService from '@/services/homeService'
import { useRequest } from 'ahooks'
import { useCallback, useState } from 'react'
import { createContainer } from 'unstated-next'

export interface SaveDataBoardInfo extends Omit<API.Home.SaveComponentsToBoardParams, 'component_name' | 'board_id'> { }
export interface SaveComponentInfo extends API.Home.SaveComponentParams {}

function useContainer() {
  // 侧边栏展开状态
  const [collapsed, setCollapsed] = useState(false)
  // 面板列表展开状态
  const [panelListCollapsed, setPanelListCollapsed] = useState(false)
  // 会话id
  const [conversationId, setConversationId] = useState<string>()
  // 当前对话数据
  const [conversationData, setConversationData] = useState<ConversationDataItem[]>([])
  // 会话列表
  const [conversations, setConversations] = useState<API.Home.ConversationsItem[]>([])
  // 请求状态
  const [status, setStatus] = useState<ThoughtChainItem['status']>()
  // 正在输入
  const [typing, setTyping] = useState(false)
  // total
  const [total, setTotal] = useState(0)

  // 时间选择loading
  const [timeSearchLoading, setTimeSearchLoading] = useState(false)

  // 保存数据看板
  const { visible: saveDataBoardVisible, key: saveDataBoardVisibleKey, action: saveDataBoardAction, selectItem: saveDataBoardSelectItem } = useModalVisible()

  // 保存组件
  const { visible: saveComponentVisible, key: saveComponentVisibleKey, action: saveComponentAction, selectItem: saveComponentSelectItem } = useModalVisible()

  /**
   * 获取会话列表
   */
  useRequest(async () => await homeService.getConversations({
    page: 1,
    size: 20,
  }), {
    onSuccess: (res) => {
      setTotal(res.page_info.total)
      setConversations(res.data)
    },
  })

  /**
   * 创建对话
   */
  const createConversation = useCallback(async (callback?: () => void) => {
    const res = await homeService.createConversation()
    setConversationId(res.id)
    setConversations((draft) => {
      return [res, ...draft]
    })
    setConversationData([])
    callback?.()
  }, [setConversations, setConversationId, setConversationData])

  /**
   * 获取历史对话
   */
  const getHistoryConversation = useCallback(async (id: string) => {
    setConversationId(id)
    setConversationData([])
    const res = await homeService.getConversationById(id)
    const result = res.messages.map((item, index) => {
      return {
        id: item.id,
        conversation_id: item.conversation_id,
        content: item.content,
        role: item.role,
        pre_message_role: res.messages[index - 1]?.role,
        time_info_extracted: item?.time_info_extracted,
        visualization_data: item?.visualization_data,
        excel_result_available: item?.excel_result_available,
        map: item?.map,
        topN: item?.topN,
        workflow_id: item.workflow_id,
      }
    })
    setConversationData(result)
  }, [setConversationData])

  /**
   * 删除对话
   */
  const deleteConversation = useCallback(async (id: string) => {
    await homeService.deleteConversation(String(id))
    // 判断当前是否选中了这个id
    if (conversationId === id) {
      setConversationId(undefined)
      setConversationData([])
    }
    setConversations((draft) => {
      return draft.filter(item => item.id !== id)
    })
  }, [conversationId, setConversationId, setConversationData])

  const onHandleTimeSearch = useCallback(async (id: string, params: API.Home.GetTimeSelectParams) => {
    setTimeSearchLoading(true)
    const res = await homeService.getTimeSelect(params)
    setConversationData((draft) => {
      return draft.map((item) => {
        if (item.id === id) {
          return {
            ...item,
            visualization_data: res.visualization_data,
            sql_execution_result: res.sql_execution_result,
            time_info_extracted: res.time_info_extracted,
            map: res.map,
            topN: res.topN,
            excel_result_available: res.excel_result_available,
          }
        }
        return item
      })
    })
    setTimeSearchLoading(false)
  }, [])

  return {
    collapsed,
    setCollapsed,
    panelListCollapsed,
    setPanelListCollapsed,
    conversationId,
    setConversationId,
    conversations,
    setConversations,
    getHistoryConversation,
    createConversation,
    conversationData,
    setConversationData,
    status,
    setStatus,
    typing,
    setTyping,
    deleteConversation,
    conversationsTotal: total,
    saveDataBoardVisible,
    saveDataBoardAction,
    saveDataBoardVisibleKey,
    onHandleTimeSearch,
    saveDataBoardSelectItem,
    timeSearchLoading,
    setTimeSearchLoading,
    saveComponentVisible,
    saveComponentAction,
    saveComponentVisibleKey,
    saveComponentSelectItem,
  }
}

const Container = createContainer(useContainer)

export type ContainerType = typeof useContainer

// eslint-disable-next-line react-refresh/only-export-components
export { useContainer }

export default Container
