import type { SaveComponentInfo } from '../../container'
import { ComponentTypeEnum } from '@/enum'
import useModalVisible from '@/hooks/useModalVisible'
import PanelItem from '@/pages/DataPanelList/components/PanelItem'
import homeService from '@/services/homeService'
import { MenuUnfoldOutlined } from '@ant-design/icons'
import { useMount, useRequest, useSetState } from 'ahooks'
import { Empty, Skeleton } from 'antd'
import { arrayMoveImmutable } from 'array-move'
import React, { useMemo, useState } from 'react'
import InfiniteScroll from 'react-infinite-scroll-component'
import { useNavigate } from 'react-router-dom'
import Container from '../../container'
import SaveCompBoard from '../SaveCompBoard'

interface IPageInfoType {
  page: number
  size: number
  total: number
}

function PanelList() {
  const { panelListCollapsed, setPanelListCollapsed } = Container.useContainer()
  const navigate = useNavigate()
  const [list, setList] = useState<API.Home.GetBoardListResultItem[]>([])

  const { visible, key, action, selectItem } = useModalVisible()

  const [pageInfo, setPageInfo] = useSetState<IPageInfoType>({
    page: 1,
    size: 10,
    total: 0,
  })

  const [hasMore, setHasMore] = useState(true)

  const { run: requestList, loading } = useRequest(async ({ page, size }: { page: number, size: number }) => await homeService.getBoardList({ page, size }), {
    onSuccess: (res: API.Home.GetBoardListResult) => {
      const hasMore = res.pagination.total > list.length + res.components.length
      setHasMore(hasMore)
      setList((prev) => {
        if (res.pagination.page === 1) {
          return res.components
        }
        return [...prev, ...res.components]
      })
      setPageInfo({
        page: res.pagination.page,
        size: res.pagination.size,
        total: res.pagination.total,
      })
    },
    manual: true,
  })

  // 置顶的组件数量
  const toppingCount = useMemo(() => {
    return list.filter(item => item.is_topping === '1').length
  }, [list])

  useMount(() => {
    requestList({
      page: pageInfo.page,
      size: pageInfo.size,
    })
  })

  const onHandleAllPanel = () => {
    navigate('/dataPanelList')
  }

  const onRefresh = () => {
    requestList({
      page: 1,
      size: pageInfo.size,
    })
  }

  const onEdit = (item: API.Home.GetBoardListResultItem) => {
    action.setTrue({
      component_name: item.name,
      component_id: item.id,
      workflow_id: item.workflow_id,
      component_info: {
        code: item.code,
        json: item.json,
        start_time: item.start_time,
        stop_time: item.stop_time,
        time_type: item.time_type,
        component_type: item.component_type,
      },
    } as SaveComponentInfo)
  }

  const onMoveUp = (index: number) => {
    const newList = arrayMoveImmutable(list, index, index - 1)
    setList(newList)
  }

  const onMoveDown = (index: number) => {
    const newList = arrayMoveImmutable(list, index, index + 1)
    setList(newList)
  }

  const onSearch = async (item: API.Home.GetBoardListResultItem, start_time: string, stop_time: string) => {
    const params = {
      workflow_id: item.workflow_id,
      new_time_end: stop_time,
      new_time_start: start_time,
    }
    const res = await homeService.getTimeSelect(params)
    setList((prev) => {
      return prev.map((draft) => {
        if (draft.id === item.id) {
          const code = item.component_type === ComponentTypeEnum.Echarts ? res.visualization_data : item.component_type === ComponentTypeEnum.Map ? res.map : res.topN
          return { ...draft, code: code ?? draft.code }
        }
        return draft
      })
    })
  }

  return (
    <React.Fragment>
      <div
        className={`h-full bg-white border-x border-solid border-[#E5E5E5] relative flex flex-col ${panelListCollapsed ? 'w-0' : 'w-[360px]'}`}
        style={{
          transition: 'width 0.3s ease-in-out',
        }}
      >
        <div className="w-full h-[48px] flex justify-between items-center px-[16px] border-b border-solid border-[#E5E5E5]">
          <div className="w-[58px]">
            <MenuUnfoldOutlined color="#999" onClick={() => setPanelListCollapsed(!panelListCollapsed)} />
          </div>
          <span className="whitespace-nowrap font-bold">我的看板</span>
          <div className="w-[58px] text-[#999] text-[14px] cursor-pointer hover:text-[#333] whitespace-nowrap" onClick={onHandleAllPanel}>查看全部</div>
        </div>
        <div className="flex-1 p-[16px] overflow-auto" id="scrollableDiv">
          <InfiniteScroll
            dataLength={list.length}
            next={() => {
              requestList({
                page: pageInfo.page + 1,
                size: pageInfo.size,
              })
            }}
            scrollableTarget="scrollableDiv"
            hasMore={hasMore}
            loader={<div className="text-center py-2"><Skeleton paragraph={{ rows: 1 }} active /></div>}
            className="!overflow-visible"
          >
            <div className="flex flex-col gap-[12px]">
              {
                list.map((item, index) => (
                  // 非置顶第一个组件没有上移
                  <PanelItem
                    key={item.id}
                    item={item}
                    disabledMoveUp={index === 0 || index === toppingCount}
                    disabledMoveDown={index === list.length - 1 || index === toppingCount - 1}
                    onRefresh={onRefresh}
                    onMoveUp={() => onMoveUp(index)}
                    onMoveDown={() => onMoveDown(index)}
                    onSearch={(start_time, stop_time) => {
                      onSearch(item, start_time, stop_time)
                    }}
                    onEdit={() => {
                      onEdit(item)
                    }}
                  />
                ))
              }
            </div>
            {
              list.length === 0 && !loading && (
                <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
              )
            }
            {
              loading && list.length === 0 && (
                <div className="flex justify-center items-center h-full">
                  <Skeleton />
                </div>
              )
            }
          </InfiniteScroll>
        </div>
        <SaveCompBoard
          key={key}
          open={visible}
          onCancel={() => {
            action.setFalse()
            onRefresh()
          }}
          selectItem={selectItem}
        />
      </div>
    </React.Fragment>
  )
}

export default PanelList
