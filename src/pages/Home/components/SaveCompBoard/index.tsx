import type { FormInstance, ModalProps } from 'antd'
import type { SaveComponentInfo } from '../../container'
import { dictInfo } from '@/dict'
import { ComponentTimeGranularityProEnum, ComponentTimeTypeEnum, TimeUnitEnum } from '@/enum/components/home'
import homeService from '@/services/homeService'
import { useMount } from 'ahooks'
import { Col, Form, Input, message, Modal, Row, Select } from 'antd'
import dayjs from 'dayjs'
import React, { useCallback, useMemo, useRef } from 'react'
import { Case, If, Switch, Then } from 'react-if'

interface FormValues {
  name: string
  time_unit: TimeUnitEnum
  time_granularity: ComponentTimeGranularityProEnum
}

interface IProps extends Omit<ModalProps, 'onCancel'> {
  selectItem: SaveComponentInfo
  onCancel: () => void
}

function SaveCompBoard(props: IProps) {
  const { selectItem, open, onCancel } = props

  const formRef = useRef<FormInstance<FormValues>>(null)

  const isShowRange = useMemo(() => {
    return selectItem?.component_info?.time_type === ComponentTimeTypeEnum.Range
  }, [selectItem?.component_info?.time_type])

  const onHandleOk = useCallback(() => {
    const values = formRef.current?.getFieldsValue()
    if (values) {
      const params = {
        ...selectItem,
        component_name: values.name,
      }
      if (values?.time_unit) {
        const infoList = [
          ...dictInfo.componentDayTimeGranularity,
          ...dictInfo.componentMonthTimeGranularity,
          ...dictInfo.componentHourTimeGranularity,
          ...dictInfo.componentYearTimeGranularity,
        ]
        const timeInfo = infoList.find(item => item.value === values.time_granularity)
        const newParams = {
          ...params,
          component_info: {
            ...params.component_info,
            start_time: dayjs(timeInfo?.start).format('YYYY-MM-DD HH:mm:ss'),
            stop_time: dayjs(timeInfo?.end).format('YYYY-MM-DD HH:mm:ss'),
          },
        }
        homeService.saveComponent(newParams)
        message.success('保存成功')
      }
      else {
        homeService.saveComponent(params)
        message.success('保存成功')
      }
      onCancel()
    }
  }, [onCancel, selectItem])

  const onTimeUnitChange = useCallback((value: TimeUnitEnum) => {
    switch (value) {
      case TimeUnitEnum.Day:
        formRef.current?.setFieldsValue({
          time_granularity: ComponentTimeGranularityProEnum.Today,
        })
        break
      case TimeUnitEnum.Month:
        formRef.current?.setFieldsValue({
          time_granularity: ComponentTimeGranularityProEnum.ThisMonth,
        })
        break
      case TimeUnitEnum.Hour:
        formRef.current?.setFieldsValue({
          time_granularity: ComponentTimeGranularityProEnum.LastOneHour,
        })
        break
      case TimeUnitEnum.Year:
        formRef.current?.setFieldsValue({
          time_granularity: ComponentTimeGranularityProEnum.ThisYear,
        })
        break
      default:

        break
    }
  }, [])

  useMount(() => {
    if (selectItem?.component_name) {
      formRef.current?.setFieldsValue({
        name: selectItem.component_name,
      })
    }
  })

  return (
    <React.Fragment>
      <Modal
        title="保存到数据看板"
        width={540}
        open={open}
        onOk={onHandleOk}
        maskClosable={false}
        classNames={{
          header: '!px-6 !py-4 !border-b !border-[#F0F0F0]',
          content: '!p-0',
          body: '!p-6',
          footer: '!px-4 !py-[10px] !border-t !border-[#F0F0F0] !mt-0',
        }}
        onCancel={onCancel}
      >
        <Form ref={formRef} labelCol={{ span: 5 }}>
          <Form.Item
            name="name"
            label="名称"
            rules={[
              {
                required: true,
                message: '请输入名称',
              },
            ]}
          >
            <Input className="h-[36px]" placeholder="请输入" />
          </Form.Item>
          <If condition={isShowRange}>
            <Then>
              <Form.Item label="默认时间范围">
                <Row gutter={6}>
                  <Col span={6}>
                    <Form.Item name="time_unit" initialValue={TimeUnitEnum.Day}>
                      <Select options={dictInfo.timeUnit} onChange={onTimeUnitChange} />
                    </Form.Item>
                  </Col>
                  <Col span={18}>
                    <Form.Item dependencies={['time_unit']}>
                      {
                        ({ getFieldValue }) => {
                          const timeUnit = getFieldValue('time_unit')
                          return (
                            <Switch>
                              <Case condition={timeUnit === TimeUnitEnum.Day}>
                                <Form.Item name="time_granularity" initialValue={ComponentTimeGranularityProEnum.Today}>
                                  <Select options={dictInfo.componentDayTimeGranularity} />
                                </Form.Item>
                              </Case>
                              <Case condition={timeUnit === TimeUnitEnum.Month}>
                                <Form.Item name="time_granularity">
                                  <Select options={dictInfo.componentMonthTimeGranularity} />
                                </Form.Item>
                              </Case>
                              <Case condition={timeUnit === TimeUnitEnum.Hour}>
                                <Form.Item name="time_granularity">
                                  <Select options={dictInfo.componentHourTimeGranularity} />
                                </Form.Item>
                              </Case>
                              <Case condition={timeUnit === TimeUnitEnum.Year}>
                                <Form.Item name="time_granularity">
                                  <Select options={dictInfo.componentYearTimeGranularity} />
                                </Form.Item>
                              </Case>
                            </Switch>
                          )
                        }
                      }
                    </Form.Item>
                  </Col>
                </Row>
              </Form.Item>
            </Then>
          </If>
        </Form>
      </Modal>
    </React.Fragment>
  )
}

export default SaveCompBoard
