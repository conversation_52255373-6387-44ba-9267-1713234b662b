import deleteIcon from '@/assets/images/delete.png'
import { HistoryOutlined, MenuFoldOutlined, PlusOutlined } from '@ant-design/icons'
import { Conversations } from '@ant-design/x'
import { Empty, Modal, Tooltip } from 'antd'
import { useMemo } from 'react'
import { Scrollbars } from 'react-custom-scrollbars'
import { useNavigate } from 'react-router-dom'
import Container from '../../container'
import styles from './index.module.less'

function SiderComp() {
  const { collapsed, conversations, conversationId, getHistoryConversation, conversationsTotal, createConversation, deleteConversation, setCollapsed } = Container.useContainer()
  const navigate = useNavigate()
  const items = useMemo(() => {
    return conversations.map(item => ({
      label: (
        <div className="flex items-center justify-between gap-[10px]">
          <div className="text-[14px] text-[#1E1E1E] w-[calc(100%-36px)] overflow-hidden text-ellipsis">
            {item.summary || item.name}
          </div>
          <div className="cursor-pointer size-[24px] hover:bg-[#b4b1b1] flex justify-center items-center rounded-[4px]">
            <Tooltip title="删除会话">
              <img
                src={deleteIcon}
                alt=""
                className="size-[20px]"
                onClick={(e) => {
                  e.stopPropagation()
                  Modal.confirm({
                    title: '提示',
                    content: '确定删除该对话吗？',
                    onOk: () => {
                      deleteConversation(item.id)
                    },
                  })
                }}
              />
            </Tooltip>
          </div>
        </div>
      ),
      key: item.id,
    }))
  }, [conversations, deleteConversation])

  const collapsedTextStyle = useMemo(() => {
    return {
      display: collapsed ? 'none' : 'block',
      whiteSpace: 'nowrap',
    }
  }, [collapsed])

  const onHandleAllConversation = () => {
    navigate('/history')
  }

  return (
    <div
      className={`overflow-hidden h-full flex flex-col bg-white relative border-r border-solid border-[#E5E5E5] ${collapsed ? styles.collapsed : styles.expanded}`}
      style={{
        transition: 'width 0.3s ease-in-out',
        transitionDuration: '0.3s',
      }}
    >
      <div className="h-[36px] flex items-center justify-between p-[8px] absolute top-0 right-0 cursor-pointer" style={collapsedTextStyle}>
        <div className="size-[24px] border border-solid border-[#e6dcdc] rounded-[4px] flex items-center justify-center" onClick={() => setCollapsed(!collapsed)}>
          <MenuFoldOutlined color="#999" />
        </div>
      </div>
      <div className="h-[48px] flex items-center text-[#165DFF] font-bold pr-[48px] pl-[24px] border-b border-solid border-[#E5E5E5]">
        <div className="w-full flex gap-[6px] cursor-pointer" onClick={() => createConversation()}>
          <PlusOutlined style={{ color: '#165DFF' }} />
          <span style={collapsedTextStyle}>新对话</span>
        </div>
      </div>
      <div className="flex-1 overflow-hidden relative">
        <div className="font-bold text-[16px] text-[#1E1E1E] px-[24px] h-[56px] max-2xl:h-[48px] flex items-center gap-[6px]">
          <HistoryOutlined />
          <span style={collapsedTextStyle}>会话历史</span>
        </div>
        <div style={collapsedTextStyle} className="h-[calc(100%-56px)]">
          {conversations.length > 0 && (
            <div className="flex-1 h-[calc(100%-56px-56px)] max-2xl:h-[calc(100%-48px-48px)]">
              <Scrollbars className="overflow-y-auto">
                <Conversations
                  activeKey={conversationId}
                  // menu={menuConfig}
                  items={items}
                  onActiveChange={(value) => {
                    getHistoryConversation(value)
                  }}
                />
              </Scrollbars>
            </div>
          )}
          {
            conversations.length === 0 && (
              <div className="flex justify-center items-center h-full">
                <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
              </div>
            )
          }
          {conversationsTotal > 20 && <div className="flex justify-center items-center bg-white h-[56px] max-2xl:h-[48px] text-center text-[16px] max-2xl:text-[14px] text-[#5046E4] font-bold cursor-pointer" onClick={onHandleAllConversation}>查看全部</div>}
        </div>
      </div>
    </div>
  )
}

export default SiderComp
