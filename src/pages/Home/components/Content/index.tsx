import type { ConversationDataItem } from '../../hooks/typings'
import { RoleEnum } from '@/enum/components/home'
import { useAutoScroll } from '@/hooks/useAutoScroll'
import useStore from '@/store'
import { MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons'
import { Sender, XRequest } from '@ant-design/x'
import { nanoid } from 'nanoid'
import { useCallback, useMemo, useRef, useState } from 'react'
import Container from '../../container'
import useStream from '../../hooks/useStream'
import SaveCompBoard from '../SaveCompBoard'
import SaveDataBoard from '../SaveDataBoard'
import Conversation from './components/Conversation'
import WelcomeComp from './components/Welcome'

function Content() {
  const { collapsed, setCollapsed, conversationId, createConversation, setConversationData, setStatus, setTyping, saveComponentSelectItem, saveDataBoardVisibleKey, saveDataBoardSelectItem, panelListCollapsed, setPanelListCollapsed, saveComponentVisibleKey, saveDataBoardVisible, saveComponentVisible, saveDataBoardAction, saveComponentAction } = Container.useContainer()
  // 取消请求
  const abortController = useRef<AbortController>(null)

  // 发送消息loading
  const [loading, setLoading] = useState(false)

  // 输入框内容
  const [value, setValue] = useState('')

  const token = useStore(draft => draft.token)

  const { domRef, onScroll, tryAutoScroll } = useAutoScroll<HTMLDivElement>()

  const { streamSuccess } = useStream({
    setConversationData,
  })

  const sendRequest = XRequest({
    baseURL: 'http://47.95.115.11:15096/api/chat/stream',
    fetch: async (url: any, options: any) => {
      const response = await fetch(url, {
        ...options,
        headers: {
          Authorization: `Bearer ${token}`,
          ...options.headers,
        },
      })
      return response
    },
  })

  const sendMessage = useCallback(async (message: string) => {
    setStatus('pending')
    const id = nanoid()
    await sendRequest.create({
      messages: [{ role: RoleEnum.USER, content: message, debug: true }],
      conversationId: String(conversationId),
    }, {
      onStream: (controller) => {
        abortController.current = controller
      },
      onSuccess: (messages) => {
        streamSuccess(messages, id)
        setStatus('success')
        setLoading(false)
        setTyping(false)
      },
      onError: () => {
        setStatus('error')
        setLoading(false)
        setTyping(false)
      },
      onUpdate: (msg) => {
        setStatus('pending')
        const data = JSON.parse(msg.data)
        const event = msg.event.trim()
        if (event === 'message') {
          const content = data?.delta?.content
          setConversationData((draft) => {
            const isExist = draft.find(item => item.id === id)
            if (isExist) {
              return draft.map((item) => {
                if (item.id === id) {
                  return { ...item, content: item.content + content }
                }
                return item
              })
            }
            return [...draft, {
              id,
              content,
              role: RoleEnum.ASSISTANT,
              conversation_id: id,
            }] as ConversationDataItem[]
          })
          setTimeout(() => {
            tryAutoScroll()
          }, 0)
        }
      },
    })
  }, [conversationId, sendRequest, setConversationData, setStatus, setTyping, streamSuccess, tryAutoScroll])

  const createUserMessage = useCallback((message: string) => {
    setConversationData((draft) => {
      return [
        ...draft,
        {
          id: nanoid(),
          content: message,
          role: RoleEnum.USER,
          conversation_id: String(conversationId),
        },
      ] as ConversationDataItem[]
    })
  }, [conversationId, setConversationData])

  // 发送消息
  const onSubmit = useCallback((message: string) => {
    // 如果会话id存在，则发送消息
    if (conversationId) {
      createUserMessage(message)
      sendMessage(message)
    }
    else {
      createConversation(() => {
        createUserMessage(message)
        sendMessage(message)
      })
    }
  }, [conversationId, createConversation, createUserMessage, sendMessage])

  const collapsedTextStyle = useMemo(() => {
    return {
      display: !collapsed ? 'none' : 'block',
    }
  }, [collapsed])

  const panelListCollapsedTextStyle = useMemo(() => {
    return {
      display: !panelListCollapsed ? 'none' : 'block',
    }
  }, [panelListCollapsed])

  return (
    <div className="flex-1 relative flex flex-col bg-white">
      <div className="h-[36px] flex items-center justify-between p-[8px] absolute top-0 left-0 cursor-pointer" style={collapsedTextStyle}>
        <div className="size-[24px] border border-solid border-[#e6dcdc] rounded-[4px] flex items-center justify-center" onClick={() => setCollapsed(!collapsed)}>
          <MenuUnfoldOutlined color="#999" />
        </div>
      </div>
      <div className="h-[36px] flex items-center justify-between p-[8px] absolute top-0 right-0 cursor-pointer" style={panelListCollapsedTextStyle}>
        <div className="size-[24px] border border-solid border-[#e6dcdc] rounded-[4px] flex items-center justify-center" onClick={() => setPanelListCollapsed(!panelListCollapsed)}>
          <MenuFoldOutlined color="#999" />
        </div>
      </div>
      <div className="py-[16px] px-[10%] overflow-y-auto flex-1 pb-[100px]" ref={domRef} onScroll={onScroll}>
        {!conversationId && <WelcomeComp />}
        {
          conversationId && <Conversation />
        }
      </div>
      <div className="w-full absolute bottom-0 px-[10%] py-[16px] flex justify-center items-center bg-white">
        <Sender
          loading={loading}
          value={value}
          onChange={(v) => {
            setValue(v)
          }}
          onSubmit={(message) => {
            setLoading(true)
            setTyping(true)
            setValue('')
            onSubmit(message)
          }}
          onCancel={() => {
            setLoading(false)
            abortController.current?.abort()
          }}
          autoSize={{ minRows: 2, maxRows: 6 }}
        />
      </div>
      <SaveDataBoard key={saveDataBoardVisibleKey} open={saveDataBoardVisible} selectItem={saveDataBoardSelectItem} onCancel={() => saveDataBoardAction.setFalse()} />
      <SaveCompBoard key={saveComponentVisibleKey} open={saveComponentVisible} selectItem={saveComponentSelectItem} onCancel={() => saveComponentAction.setFalse()} />
    </div>
  )
}

export default Content
