// import homeService from '@/services/homeService'
import { Welcome } from '@ant-design/x'
// import { useRequest } from 'ahooks'
// import Container from '../../../../container'

function WelcomeComp() {
  // const { latestConversationId } = Container.useContainer()
  // const data = useRequest(async () => await homeService.getSuggestedQuestions({
  //   message_id: latestConversationId as unknown as number,
  //   message_limit: 3,
  //   max_token_limit: 3000,
  // }))

  return (
    <div className="flex flex-col">
      <Welcome
        icon="https://mdn.alipayobjects.com/huamei_iwk9zp/afts/img/A*s5sNRo5LjfQAAAAAAAAAAAAADgCCAQ/fmt.webp"
        title="Hi，我是和信 Chat BI"
        description="我可以根据你的问题，分析数据、生成图表。"
        style={{
          background: 'linear-gradient(97deg, #f2f9fe 0%, #f7f3ff 100%)',
        }}
      />
      {/* <div className="flex flex-col px-[84px] mt-[16px]">
        <div className="text-[#1E1E1E] text-[14px] font-medium">猜你想问</div>
        <div className="flex flex-col gap-[8px] mt-[16px]">
          {data.data?.questions.map(item => (
            <div key={item} className="text-[#203FDF] text-[14px] font-normal cursor-pointer">{item}</div>
          ))}
        </div>
      </div> */}
    </div>
  )
}

export default WelcomeComp
