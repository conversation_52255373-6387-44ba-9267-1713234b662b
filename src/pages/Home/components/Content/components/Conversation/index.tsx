import React, { useCallback, useMemo } from 'react'
import Container from '../../../../container'
import BubbleItem from '../BubbleItem'

function Conversation() {
  const { conversationData, setConversationData, typing } = Container.useContainer()

  const data = useMemo(() => {
    return conversationData.map((item) => {
      return {
        ...item,
        content: {
          ...item,
        },
      }
    })
  }, [conversationData])

  const updateConversationData = useCallback((id: string, data: any) => {
    setConversationData((draft) => {
      return draft.map((item) => {
        return item.id === id ? { ...item, ...data } : item
      })
    })
  }, [setConversationData])

  return (
    <React.Fragment>
      {
        data.map((item) => {
          return <BubbleItem key={item.id} item={item} typing={typing} updateConversationData={updateConversationData} />
        })
      }
    </React.Fragment>
  )
}

export default Conversation
