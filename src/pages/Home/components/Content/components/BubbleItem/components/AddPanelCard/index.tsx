import { DownloadOutlined, SaveOutlined } from '@ant-design/icons'
import { Button } from 'antd'
import React from 'react'

interface IProps {
  children: React.ReactNode
  toolbar?: boolean
  onDownload?: () => void
  onSave?: () => void
  onComponentSave?: () => void
}

function AddPanelCard(props: IProps) {
  const { children, toolbar = true, onDownload, onSave, onComponentSave } = props
  return (
    <React.Fragment>
      <div className="w-full h-full rounded-[8px] bg-white flex flex-col">
        {toolbar && (
          <div className="flex justify-end p-[8px]">
            <div className="flex gap-[8px] items-center">
              <div className="flex">
                {onDownload && (
                  <Button type="link" variant="link" icon={<DownloadOutlined />} onClick={onDownload}>
                    下载
                  </Button>
                )}
                {onSave && (
                  <Button type="link" variant="link" icon={<SaveOutlined />} onClick={onSave}>
                    保存至数据面板
                  </Button>
                )}
                {onComponentSave && (
                  <Button type="link" variant="link" icon={<SaveOutlined />} onClick={onComponentSave}>
                    保存
                  </Button>
                )}
              </div>
            </div>
          </div>
        )}
        <div className="flex-1 flex justify-center items-center p-[8px]">
          {children}
        </div>
      </div>
    </React.Fragment>
  )
}

export default AddPanelCard
