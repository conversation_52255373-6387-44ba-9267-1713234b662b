import type { StreamSqlExecutionResultType } from '@/pages/Home/hooks/typings'
import { safeJsonParse } from '@/utils'
import React, { useMemo } from 'react'

interface IProps {
  value?: string
}

function SqlResult(props: IProps) {
  const { value } = props

  const sqlResult = useMemo(() => {
    const parseData = safeJsonParse(value, {})
    return parseData as Partial<StreamSqlExecutionResultType['data']>
  }, [value])

  return (
    <React.Fragment>
      <div className="flex flex-col w-full bg-white rounded-[8px] p-[8px]">
        <div className="text-[16px] font-bold border-b border-[#E5E5E5] p-[8px]">查询结果</div>
        <div className="flex flex-col gap-[16px] p-[8px]">
          {
            Object.entries(sqlResult?.rows || {}).map(([key, value]) => (
              <div key={key} className="flex gap-[4px]">
                <div className="text-[14px] font-bold">
                  {key}
                  :
                </div>
                <div className="text-[14px]">{value}</div>
              </div>
            ))
          }
        </div>
      </div>
    </React.Fragment>
  )
}

export default SqlResult
