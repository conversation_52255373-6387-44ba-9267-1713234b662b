/* eslint-disable react/no-array-index-key */
import type { StreamRankDataType } from '@/pages/Home/hooks/typings'
import { ComponentTypeEnum, GridColSpanEnum } from '@/enum'
import { getColumns, safeJsonParse } from '@/utils'
import { faCrown, faTrophy } from '@fortawesome/free-solid-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import React, { useCallback, useMemo } from 'react'
import { Case, Default, Switch } from 'react-if'
import AddPanelCard from '../AddPanelCard'

export interface IProps {
  value?: string
  toolbar?: boolean
  onChange?: (value: string, config: Record<string, any>) => void
  onComponentChange?: (value: string, config: Record<string, any>) => void
}

// 排行榜组件
function TopN(props: IProps) {
  const { value, toolbar = true, onChange, onComponentChange } = props

  const topInfo = useMemo(() => {
    const data = safeJsonParse(value, {})
    return data as StreamRankDataType['data']
  }, [value])

  const columns = useMemo(() => {
    return getColumns(topInfo?.data)
  }, [topInfo])

  // 获取奖杯图标
  const getTrophyIcon = useCallback((rank: number) => {
    return (
      <React.Fragment>
        <Switch>
          <Case condition={rank === 0}>
            <div className="size-[36px] rounded-full bg-gradient-to-br from-yellow-300 via-yellow-500 to-amber-500 flex items-center justify-center shadow-lg transition-all duration-300 hover:scale-110 hover:shadow-yellow-300/50 border-2 border-yellow-200">
              <FontAwesomeIcon icon={faCrown} className="text-white text-lg" />
            </div>
          </Case>
          <Case condition={rank === 1}>
            <div className="size-[32px] rounded-full bg-gradient-to-br from-slate-200 via-gray-300 to-gray-400 flex items-center justify-center shadow-lg transition-all duration-300 hover:scale-110 hover:shadow-gray-300/50 border-2 border-gray-200">
              <FontAwesomeIcon icon={faTrophy} className="text-white" />
            </div>
          </Case>
          <Case condition={rank === 2}>
            <div className="size-[30px] rounded-full bg-gradient-to-br from-amber-500 via-amber-600 to-amber-700 flex items-center justify-center shadow-lg transition-all duration-300 hover:scale-110 hover:shadow-amber-500/50 border-2 border-amber-400">
              <FontAwesomeIcon icon={faTrophy} className="text-white" />
            </div>
          </Case>
          <Default>
            <div className="size-[28px] rounded-full bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center text-[14px] text-white font-bold shadow-md transition-all duration-300 hover:scale-110 border border-blue-300">
              {rank + 1}
            </div>
          </Default>
        </Switch>
      </React.Fragment>
    )
  }, [])

  // eslint-disable-next-line unused-imports/no-unused-vars
  const onSave = useCallback(() => {
    onChange?.(value ?? '', {
      json: '{"x":0,"y":0,"w":3,"h":20}',
      component_type: ComponentTypeEnum.TopN,
    })
  }, [value, onChange])

  const onComponentSave = useCallback(() => {
    onComponentChange?.(value ?? '', {
      json: GridColSpanEnum.Col1,
      component_type: ComponentTypeEnum.TopN,
    })
  }, [onComponentChange, value])

  return (
    <React.Fragment>
      <AddPanelCard
        // onSave={onSave}
        onComponentSave={onComponentSave}
        toolbar={toolbar}
      >
        <div className="w-[320px] flex flex-col backdrop-blur-md bg-white/30 rounded-xl shadow-xl overflow-hidden border border-white/40 relative mb-[16px]">
          {/* 背景渐变光晕 */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-400/10 via-purple-300/10 to-pink-300/10 z-0"></div>

          {/* 顶部装饰光带 */}
          <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500"></div>

          {/* 标题区域 */}
          <div className="relative z-10 font-bold text-[22px] mb-[16px] text-center py-4 text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600 border-b border-white/40">
            <span className="drop-shadow-md text-[#2268FA]">{topInfo.title || '排行榜'}</span>
          </div>
          <div className="flex flex-col px-2">
            {
              topInfo?.data?.map((item, index) => {
                const isTopThree = index < 3
                return (
                  <div
                    key={index}
                    className={`flex items-center justify-between px-[12px] py-[12px] mb-2 rounded-lg
                                  ${isTopThree
                    ? 'bg-gradient-to-r from-blue-50/80 to-purple-50/80 backdrop-blur-sm border border-white/40 shadow-sm'
                    : 'bg-white/30 hover:bg-white/50'} 
                                  transition-all duration-300 hover:shadow-md hover:translate-x-1`}
                  >
                    <div className="flex items-center gap-[12px]">
                      <div className="flex">
                        {getTrophyIcon(index)}
                      </div>
                      <div className={`text-[16px] font-medium ${isTopThree ? 'font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-700 to-purple-700' : 'text-gray-700'}`}>
                        {item[columns?.[0]?.dataIndex]}
                      </div>
                    </div>
                    <div className={`text-[18px] font-bold ${isTopThree
                      ? 'text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600'
                      : 'text-gray-800'}`}
                    >
                      {item[columns?.[1]?.dataIndex]}
                    </div>
                  </div>
                )
              })
            }
          </div>
        </div>
      </AddPanelCard>
    </React.Fragment>
  )
}

export default TopN
