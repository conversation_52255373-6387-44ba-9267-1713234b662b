import type { StreamExcelDataType } from '@/pages/Home/hooks/typings'
import { getColumns, safeJsonParse } from '@/utils'
import { Button, Table } from 'antd'
import saveAs from 'file-saver'
import { nanoid } from 'nanoid'
import React, { useCallback, useMemo } from 'react'

interface IProps {
  value?: string
}

function ExcelResult(props: IProps) {
  const { value } = props

  const excelResult = useMemo(() => {
    const parseData = safeJsonParse(value, {})
    return parseData as Partial<StreamExcelDataType['data']>
  }, [value])

  const onSave = useCallback(() => {
    saveAs(excelResult?.download_url ?? '', excelResult?.excel_filename ?? '')
  }, [excelResult])

  return (
    <React.Fragment>
      <div className="w-full rounded-[8px] bg-white p-[8px]">
        <div className="flex flex-col w-full bg-white gap-[16px]">
          <Table
            dataSource={excelResult?.data}
            rowKey={() => nanoid()}
            pagination={false}
            scroll={{ y: 350 }}
            columns={getColumns(excelResult?.data ?? [])}
          />
          <div className="text-[#999999]">
            {excelResult?.message}
          </div>
          <Button
            type="primary"
            className="w-[150px]"
            onClick={onSave}
          >
            下载
          </Button>
        </div>
      </div>
    </React.Fragment>
  )
}

export default ExcelResult
