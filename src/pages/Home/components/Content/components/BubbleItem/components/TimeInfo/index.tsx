import type { StreamTimeInfoType } from '@/pages/Home/hooks/typings'
import type { FormInstance } from 'antd'
import type { FormRefType } from '../..'
import { dictInfo } from '@/dict'
import { TimeTypeEnum } from '@/enum/components/home'
import Container from '@/pages/Home/container'
import { safeJsonParse } from '@/utils'
import { Button, DatePicker, Form } from 'antd'
import dayjs from 'dayjs'
import React, { useCallback, useEffect, useMemo } from 'react'

interface IProps {
  value?: string
  onChange: () => void
  formRef: React.RefObject<FormInstance<FormRefType> | null>
}

function TimeInfo(props: IProps) {
  const { value, formRef, onChange } = props

  const { timeSearchLoading } = Container.useContainer()

  const timeInfo = useMemo(() => {
    const parseData = safeJsonParse(value, {})
    return parseData as Partial<StreamTimeInfoType['data']>
  }, [value])

  const disabledDate = useCallback((current: dayjs.Dayjs) => {
    return current < dayjs(timeInfo?.start_time) || current > dayjs(timeInfo?.end_time)
  }, [timeInfo])

  useEffect(() => {
    const time_granularity = timeInfo?.time_granularity
    if (time_granularity) {
      const unit = dictInfo.componentTimeGranularityToTimeUnit[time_granularity]
      formRef.current?.setFieldsValue({
        unit,
      })
    }
    if (timeInfo?.time_type === TimeTypeEnum.Point) {
      formRef.current?.setFieldsValue({
        time: dayjs(timeInfo?.end_time),
      })
    }
    if (timeInfo?.time_type === TimeTypeEnum.Range) {
      formRef.current?.setFieldsValue({
        timeRang: [dayjs(timeInfo?.start_time), dayjs(timeInfo?.end_time)],
      })
    }
  }, [formRef, timeInfo])

  return (
    <React.Fragment>
      <div className="flex gap-[16px] items-center w-full bg-white p-[16px] rounded-[8px]">
        <div className="font-bold">
          时间：
        </div>
        <Form ref={formRef} layout="inline">
          <React.Fragment>
            {
              timeInfo?.time_type === TimeTypeEnum.Point && (
                <Form.Item name="time">
                  <DatePicker
                    disabledDate={disabledDate}
                    showTime
                    format="YYYY-MM-DD HH:mm:ss"
                  />
                </Form.Item>
              )
            }
            {
              timeInfo?.time_type === TimeTypeEnum.Range && (
                <Form.Item name="timeRang">
                  <DatePicker.RangePicker
                    disabledDate={disabledDate}
                    showTime
                    format="YYYY-MM-DD HH:mm:ss"
                  />
                </Form.Item>
              )
            }
          </React.Fragment>
        </Form>
        <React.Fragment>
          <Button
            type="primary"
            onClick={() => onChange()}
            loading={timeSearchLoading}
          >
            查询
          </Button>
        </React.Fragment>
      </div>
    </React.Fragment>
  )
}

export default TimeInfo
