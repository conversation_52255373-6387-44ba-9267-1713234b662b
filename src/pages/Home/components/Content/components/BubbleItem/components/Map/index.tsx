import type { StreamEchartsMapDataType } from '@/pages/Home/hooks/typings'
// 或者使用更标准的GeoJSON类型定义
import type { Feature, FeatureCollection } from 'geojson'
import { ComponentTypeEnum, GridColSpanEnum } from '@/enum'
import { getColumns, safeJsonParse } from '@/utils'
import { centroid } from '@turf/turf'
import * as echarts from 'echarts'
import EChartsReact from 'echarts-for-react'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import AddPanelCard from '../AddPanelCard'
import styles from './index.module.less' // 导入样式模块

// 地图状态接口
interface MapState {
  name: string
  level: 'country' | 'province'
  code: string
  parent: MapState | null
  center?: [number, number] // 地图中心点坐标
}

// ECharts点击事件参数接口
interface EChartsClickParams {
  name: string
  value?: number
  data?: {
    name: string
    value: number
    code: string
  }
  [key: string]: any
}

export interface IProps {
  value?: string
  toolbar?: boolean
  onChange?: (value: string, config: Record<string, any>) => void
  onComponentChange?: (value: string, config: Record<string, any>) => void
}

// 使用命名函数组件而非匿名导出函数
function ChinaMap(props: IProps) {
  const { value, toolbar = true, onChange, onComponentChange } = props
  const curRef = useRef<EChartsReact>(null)

  const mapInfo = useMemo(() => {
    const mapData = safeJsonParse(value, [])
    return mapData as Partial<StreamEchartsMapDataType['data']>
  }, [value])

  const keyInfo = useMemo(() => {
    const columns = getColumns(mapInfo?.data || [])
    return {
      nameKey: columns[0]?.dataIndex,
      valueKey: columns[1]?.dataIndex,
    }
  }, [mapInfo])

  const mapAxiosData = useMemo(() => {
    return mapInfo?.data?.map((item: Record<string, string>) => {
      return {
        name: item[keyInfo.nameKey],
        value: item[keyInfo.valueKey],
      }
    }) as {
      name: string
      value: string
    }[]
  }, [keyInfo.nameKey, keyInfo.valueKey, mapInfo?.data])

  // 在组件初始渲染后输出数据用于调试
  useEffect(() => {
    if (mapAxiosData?.length) {
      console.log('地图数据有效值数量:', mapAxiosData.length)
    }
    else {
      console.warn('地图数据为空或无效')
    }
  }, [mapAxiosData])

  // 状态管理
  const [mapData, setMapData] = useState<FeatureCollection | null>(null) // 当前地图数据
  const [currentMap, setCurrentMap] = useState<MapState>({
    name: '全国',
    level: 'country',
    code: '100000',
    parent: null,
    center: [104.114129, 37.550339], // 默认中国地图中心点
  }) // 当前地图信息
  const [loading, setLoading] = useState<boolean>(false) // 加载状态
  const [error, setError] = useState<string | null>(null) // 错误状态

  // 使用useCallback缓存返回上一级地图的函数
  const goBackToParent = useCallback(() => {
    if (currentMap.parent) {
      // 保存父级地图信息以进行完全重置
      const parent = { ...currentMap.parent }
      setCurrentMap(parent)
    }
  }, [currentMap.parent])

  // 使用useCallback缓存地图点击事件处理函数
  const handleMapClick = useCallback((params: EChartsClickParams) => {
    // 如果点击了区域，且当前是全国地图（限制只能下钻到省级）
    if (params.name && params.data && currentMap.level === 'country') {
      if (!params.data.code) {
        return
      }

      // 下钻到省级
      setCurrentMap({
        name: params.name,
        level: 'province',
        code: params.data.code,
        parent: { ...currentMap }, // 创建当前状态的深拷贝作为父级
        // 初始使用当前地图中心点，后续在数据加载后会更新
        center: currentMap.center,
      })
    }
  }, [currentMap])

  // 处理背景点击事件（返回上一级）
  const handleChartBackgroundClick = useCallback(() => {
    if (currentMap.level !== 'country' && currentMap.parent) {
      goBackToParent()
    }
  }, [currentMap.level, currentMap.parent, goBackToParent])

  // 加载地图数据
  useEffect(() => {
    let isMounted = true // 添加变量标记组件是否已卸载

    const fetchMapData = async () => {
      setLoading(true)
      setError(null)

      try {
        let url = ''
        if (currentMap.level === 'country') {
          // 全国地图
          url = 'https://geojson.cn/api/china/1.6.2/china.json'
        }
        else {
          // 省级地图
          url = `https://geojson.cn/api/china/${currentMap.code}.json`
        }

        const response = await fetch(url)
        if (!response.ok) {
          throw new Error(`Failed to fetch map data: ${response.statusText}`)
        }

        const data = await response.json()

        // 确保组件仍然挂载
        if (!isMounted)
          return

        // 确保数据符合FeatureCollection类型
        if (data.type !== 'FeatureCollection') {
          data.type = 'FeatureCollection'
        }

        // 如果是省级地图，计算中心点
        if (currentMap.level === 'province' && data.features && data.features.length > 0) {
          const province = data.features[0]
          if (province && province.geometry) {
            try {
              const featureCentroid = centroid(province.geometry).geometry.coordinates as [number, number]

              // 更新当前地图状态，添加中心点
              if (isMounted) {
                setCurrentMap(prev => ({
                  ...prev,
                  center: featureCentroid,
                }))
              }
            }
            catch (error) {
              console.error('Error calculating centroid:', error)
            }
          }
        }

        // 如果组件仍然挂载，更新状态
        if (isMounted) {
          setMapData(data as FeatureCollection)
          // 注册地图 - 使用类型断言解决类型不匹配问题
          echarts.registerMap('current-map', data as any)
        }
      }
      catch (err: unknown) {
        console.error('Error fetching map data:', err)
        if (isMounted) {
          setError(err instanceof Error ? err.message : String(err))
        }
      }
      finally {
        if (isMounted) {
          setLoading(false)
        }
      }
    }

    fetchMapData()

    // 清理函数，防止内存泄漏
    return () => {
      isMounted = false
    }
  }, [currentMap.level, currentMap.code])

  // 确保背景点击事件被正确绑定
  useEffect(() => {
    // 确保curRef和chart实例存在
    const chart = curRef.current?.getEchartsInstance()
    if (!chart)
      return

    // 绑定背景点击事件
    const clickHandler = (params: any) => {
      // 如果点击区域组件类型不是'series'，则视为背景点击
      if (!params.componentType || params.componentType !== 'series') {
        handleChartBackgroundClick()
      }
      else {
        // 这是区域点击，交给handleMapClick处理
        handleMapClick(params)
      }
    }

    // 先解绑之前可能存在的事件，再绑定新的事件
    chart.off('click')
    chart.on('click', clickHandler)

    // 清理函数
    return () => {
      // 组件卸载时移除事件监听器，避免内存泄漏
      chart.off('click')
    }
  }, [currentMap, mapData, handleMapClick, handleChartBackgroundClick])

  // 使用useMemo缓存地图配置，避免不必要的重新计算
  const options = useMemo(() => {
    if (!mapData) {
      return {
        title: {
          text: '加载中...',
          left: 'center',
        },
      }
    }

    // 确保有正确的地图数据
    const mapDataFeatures = mapData.features || []

    // 提前计算处理数据，避免在渲染时计算
    const seriesData = mapDataFeatures.map((feature: Feature) => {
      // 确保code属性正确提取并映射
      const code = feature.properties?.code || feature.properties?.adcode || ''
      const name = feature.properties?.name || ''

      // 改进匹配逻辑，处理名称格式差异
      const cleanMapName = name.replace(/(省|市|自治区|特别行政区|壮族|维吾尔|回族|藏族)$/g, '')

      // 查找对应的数据值，尝试多种匹配方式
      const matchedData = mapAxiosData?.find((item) => {
        const cleanDataName = item.name.replace(/(省|市|自治区|特别行政区|壮族|维吾尔|回族|藏族)$/g, '')
        return cleanDataName === cleanMapName
          || item.name === name
          || item.name.includes(cleanMapName)
          || cleanMapName.includes(item.name)
      })

      const value = matchedData ? Number(matchedData.value) : 0

      return {
        name,
        value,
        code,
      }
    })

    // 计算数据的最大值和最小值，用于visualMap
    const values = seriesData.map(item => item.value).filter(value => !Number.isNaN(value) && value > 0)
    const max = values.length > 0 ? Math.max(...values) : 100
    const min = values.length > 0 ? Math.min(...values) : 0

    // 确保存在数据差异以显示不同颜色
    const hasValueDifference = max > min

    return {
      title: {
        text: `${currentMap.name}地图`,
        textStyle: {
          color: '#000',
        },
        left: 'center',
      },
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c}',
      },
      // 添加visualMap组件，分5个颜色等级
      // eslint-disable-next-line style/multiline-ternary
      visualMap: hasValueDifference ? {
        type: 'piecewise',
        pieces: [
          { min, max: min + (max - min) / 5 },
          { min: min + (max - min) / 5, max: min + 2 * (max - min) / 5 },
          { min: min + 2 * (max - min) / 5, max: min + 3 * (max - min) / 5 },
          { min: min + 3 * (max - min) / 5, max: min + 4 * (max - min) / 5 },
          { min: min + 4 * (max - min) / 5, max },
        ],
        inRange: {
          color: ['#e0f3f8', '#abd9e9', '#74add1', '#4575b4', '#313695'],
        },
        text: ['高', '低'],
        calculable: true,
        orient: 'horizontal',
        left: 'center',
        bottom: '5%',
      } : {
        // 当所有值相同时使用单一颜色
        type: 'continuous',
        min: 0,
        max: max || 100,
        inRange: {
          color: ['#74add1'],
        },
        text: ['值'],
        calculable: false,
        orient: 'horizontal',
        left: 'center',
        bottom: '5%',
      },
      series: [
        {
          name: currentMap.name,
          type: 'map',
          map: 'current-map',
          // 使用当前地图状态中的中心点
          center: currentMap.center,
          scaleLimit: {
            min: 0.5,
            max: 10,
          },
          label: {
            show: true,
            position: [1, 100],
            fontSize: 12,
            offset: [2, 0],
            align: 'left',
          },
          itemStyle: {
            areaColor: '#fff',
            borderColor: '#8ab6d1',
            emphasis: {
              areaColor: '#e6b600',
              label: {
                color: '#000',
              },
            },
          },
          roam: false,
          zoom: 1,
          data: seriesData,
        },
      ],
    }
  }, [mapData, currentMap.name, currentMap.center, mapAxiosData])

  // 强制更新图表 - 解决返回后无法下钻的问题
  useEffect(() => {
    const chart = curRef.current?.getEchartsInstance()
    if (chart && mapData) {
      chart.setOption(options, true) // true表示不合并，完全替换选项
    }
  }, [mapData, options])

  const onDownload = useCallback(() => {

  }, [])

  // eslint-disable-next-line unused-imports/no-unused-vars
  const onSave = useCallback(() => {
    onChange?.(value ?? '', {
      component_type: ComponentTypeEnum.Map,
      json: '{"x":0,"y":0,"w":5,"h":10}',
    })
  }, [value, onChange])

  const onComponentSave = useCallback(() => {
    onComponentChange?.(value ?? '', {
      json: GridColSpanEnum.Col2,
      component_type: ComponentTypeEnum.Map,
    })
  }, [onComponentChange, value])
  // 渲染加载和错误状态
  if (loading && !mapData) {
    return <div>加载地图数据中...</div>
  }

  if (error) {
    return (
      <div>
        加载地图出错:
        {error}
      </div>
    )
  }

  return (
    <AddPanelCard
      onDownload={onDownload}
      // onSave={onSave}
      onComponentSave={onComponentSave}
      toolbar={toolbar}
    >
      <div className={styles.mapContainer}>
        {/* 面包屑导航 */}
        <div className={styles.mapBreadcrumb}>
          <span
            className={`${styles.breadcrumbItem} ${currentMap.level === 'country' ? styles.active : ''}`}
            onClick={() => currentMap.level !== 'country' && setCurrentMap({
              name: '全国',
              level: 'country',
              code: '100000',
              parent: null,
              center: [104.114129, 37.550339],
            })}
          >
            全国
          </span>
          {currentMap.level === 'province' && (
            <>
              <span className={styles.breadcrumbSeparator}> &gt; </span>
              <span className={`${styles.breadcrumbItem} ${styles.active}`}>{currentMap.name}</span>
            </>
          )}
        </div>
        {/* 地图组件 */}
        <div className={styles.bazaarMap}>
          {mapData && (
            <EChartsReact
              option={options}
              ref={curRef}
              style={{ width: '100%', height: toolbar ? '350px' : '100%' }}
              lazyUpdate={false}
              notMerge={true}
            />
          )}
        </div>
      </div>
    </AddPanelCard>
  )
}

export default ChinaMap
