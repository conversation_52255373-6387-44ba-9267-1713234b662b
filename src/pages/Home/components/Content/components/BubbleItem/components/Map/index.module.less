/* 地图容器样式 */
.mapContainer {
  position: relative;
  width: 100%;
  height: 100%;
}

/* 面包屑导航样式 */
.mapBreadcrumb {
  position: absolute;
  top: 10px;
  left: 10px;
  padding: 6px 10px;
  font-size: 14px;
  color: #333;
  z-index: 10;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 4px;
}

.breadcrumbItem {
  cursor: pointer;
  
  border: 1px solid #56a0e5;
  padding: 5px 10px;
  border-radius: 4px;
  
  &:hover {
    border-color: #1890ff;
  }
  
  &.active {
    color: #1890ff;
    font-weight: bold;
  }
}

.breadcrumbSeparator {
  margin: 0 8px;
  color: #999;
}

/* 返回按钮样式 */
.mapBackButton {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 5px 10px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background-color: white;
  cursor: pointer;
  
  &:hover {
    color: #1890ff;
    border-color: #1890ff;
  }
}

/* 地图样式 */
.bazaarMap {
  width: 100%;
  height: 100%;
  position: relative;
} 