import type { StreamVisualizationDataType } from '@/pages/Home/hooks/typings'
import type { EChartsInstance } from 'echarts-for-react'
import { ComponentTypeEnum, GridColSpanEnum } from '@/enum'
import { safeJsonParse } from '@/utils'
import ReactECharts from 'echarts-for-react'
import saveAs from 'file-saver'
import React, { useCallback, useMemo, useRef } from 'react'
import AddPanelCard from '../AddPanelCard'

interface IProps {
  value?: string
  toolbar?: boolean
  onChange?: (value: string, config: Record<string, any>) => void
  onComponentChange?: (value: string, config: Record<string, any>) => void
}

function Visualization(props: IProps) {
  const { value, toolbar = true, onChange, onComponentChange } = props

  const chartRef = useRef<EChartsInstance>(null)

  const visualizationData = useMemo(() => {
    const parseData = safeJsonParse(value, {})
    return parseData as Partial<StreamVisualizationDataType['data']>
  }, [value])

  const options = useMemo(() => {
    return {
      ...visualizationData?.echarts_option ?? {},
      grid: {
        top: 50,
        left: 50,
        right: 10,
        bottom: 20,
      },
      legend: {
        right: 0,
      },
      toolbox: {
        show: false,
      },
    }
  }, [visualizationData?.echarts_option])

  const onDownload = useCallback(() => {
    const echartsInstance = chartRef.current.getEchartsInstance()
    const imageUrl = echartsInstance.getDataURL({
      type: 'png',
      pixelRatio: 2,
      backgroundColor: '#fff',
    })
    saveAs(imageUrl, 'chart.png')
  }, [])

  // eslint-disable-next-line unused-imports/no-unused-vars
  const onSave = useCallback(() => {
    onChange?.(value ?? '', {
      json: '{"x":0,"y":0,"w":4,"h":10}',
      component_type: ComponentTypeEnum.Echarts,
    })
  }, [value, onChange])

  const onComponentSave = useCallback(() => {
    onComponentChange?.(value ?? '', {
      json: GridColSpanEnum.Col2,
      component_type: ComponentTypeEnum.Echarts,
    })
  }, [onComponentChange, value])

  return (
    <React.Fragment>
      <AddPanelCard
        onDownload={onDownload}
        // onSave={onSave}
        onComponentSave={onComponentSave}
        toolbar={toolbar}
      >
        <div className="h-full flex flex-col w-full">
          <ReactECharts
            ref={chartRef}
            notMerge
            option={options}
            style={{ height: toolbar ? '350px' : '100%' }}
          />
        </div>
      </AddPanelCard>
    </React.Fragment>
  )
}

export default Visualization
