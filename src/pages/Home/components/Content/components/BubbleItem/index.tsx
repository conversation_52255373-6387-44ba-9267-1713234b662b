/* eslint-disable react-dom/no-dangerously-set-innerhtml */
import type { TimeUnitEnum } from '@/enum'
import type { ConversationDataItem } from '@/pages/Home/hooks/typings'
import type { FormInstance, GetProps } from 'antd'
import type dayjs from 'dayjs'
import { dictInfo } from '@/dict'
import { ComponentTimeGranularityEnum, ComponentTypeEnum, RoleEnum, TimeTypeEnum } from '@/enum'
import Container from '@/pages/Home/container'
import { judgeEmptyJson, safeJsonParse } from '@/utils'
import { UserOutlined } from '@ant-design/icons'
import { Bubble } from '@ant-design/x'
import { Typography } from 'antd'
import markdownit from 'markdown-it'
import { useCallback, useMemo, useRef } from 'react'
import ExcelResult from './components/ExcelResult'
import ChinaMap from './components/Map'
// import SqlResult from './components/SqlResult'
import TimeInfo from './components/TimeInfo'
import TopN from './components/TopN'
import Visualization from './components/Visualization'

export interface FormRefType {
  unit: TimeUnitEnum
  time: dayjs.Dayjs
  timeRang: dayjs.Dayjs[]
}

interface IProps {
  item: Omit<ConversationDataItem, 'content'> & {
    content: ConversationDataItem
  }
  updateConversationData: (id: string, data: any) => void
  typing: boolean
}

const md = markdownit({ html: true, breaks: true })

const fooAvatar = {
  backgroundColor: '#1890ff',
  color: '#fff',
}

const barAvatar: React.CSSProperties = {
  color: '#fff',
  backgroundColor: '#87d068',
}

const hideAvatar: React.CSSProperties = {
  visibility: 'hidden',
}

function BubbleItem(props: IProps) {
  const { item, typing } = props

  const { saveDataBoardAction, saveComponentAction, onHandleTimeSearch } = Container.useContainer()

  const formRef = useRef<FormInstance<FormRefType>>(null)

  // 当前时间选择器类型
  const timeType = useMemo(() => {
    const parseData = safeJsonParse(item.time_info_extracted, {})
    return parseData.time_type as TimeTypeEnum
  }, [item.time_info_extracted])

  const getFormatFormValue = useCallback(() => {
    const formValues = formRef.current?.getFieldsValue()
    if (formValues) {
      return {
        start_time: timeType === TimeTypeEnum.Point ? '' : formValues?.timeRang[0]?.format('YYYY-MM-DD HH:mm:ss'),
        stop_time: timeType === TimeTypeEnum.Point ? formValues?.time?.format('YYYY-MM-DD HH:mm:ss') : formValues?.timeRang[1]?.format('YYYY-MM-DD HH:mm:ss'),
      }
    }
    return false
  }, [timeType])

  // 保存组件到数据看板
  const onHandleVisualizationChange = useCallback((value: string, config?: Record<string, any>) => {
    const values = getFormatFormValue()
    if (!values) {
      return
    }
    const params = {
      workflow_id: item.workflow_id,
      component_info: {
        code: value,
        json: '{"x":0,"y":0,"w":2,"h":10}',
        start_time: values.start_time,
        stop_time: values.stop_time,
        time_granularity: ComponentTimeGranularityEnum.Day,
        time_type: dictInfo.timeTypeToComponentTimeType[timeType],
        component_type: ComponentTypeEnum.Echarts,
        ...(config ?? {}),
      },
    }
    saveDataBoardAction.setTrue(params)
  }, [getFormatFormValue, item.workflow_id, saveDataBoardAction, timeType])

  // 保存组件到组件
  const onComponentChange = useCallback((value: string, config?: Record<string, any>) => {
    const values = getFormatFormValue()
    if (!values) {
      return
    }
    const params = {
      workflow_id: item.workflow_id,
      component_info: {
        code: value,
        start_time: values.start_time,
        stop_time: values.stop_time,
        time_type: dictInfo.timeTypeToComponentTimeType[timeType],
        component_type: ComponentTypeEnum.Echarts,
        ...(config ?? {}),
      },
    }
    saveComponentAction.setTrue(params)
  }, [getFormatFormValue, item.workflow_id, saveComponentAction, timeType])

  // 处理时间选择
  const onTimeChange = useCallback(() => {
    const values = getFormatFormValue()
    if (!values) {
      return
    }
    const params = {
      workflow_id: item.workflow_id,
      new_time_end: values.stop_time,
      new_time_start: values.start_time,
    }
    onHandleTimeSearch(item.id, params)
  }, [getFormatFormValue, item.id, item.workflow_id, onHandleTimeSearch])

  const renderMarkdown = useCallback(() => {
    return (
      <Typography>
        <div className="flex flex-col">
          <div dangerouslySetInnerHTML={{ __html: md.render(item.content.content) }} />
          <div className="w-full flex flex-col gap-[16px]">
            {judgeEmptyJson(item?.time_info_extracted) && (
              <TimeInfo
                value={item.time_info_extracted}
                formRef={formRef}
                onChange={onTimeChange}
              />
            )}
            {judgeEmptyJson(item?.visualization_data)
              && (
                <Visualization
                  value={item.visualization_data}
                  onChange={onHandleVisualizationChange}
                  onComponentChange={onComponentChange}
                />
              )}
            {judgeEmptyJson(item?.excel_result_available)
              && (
                <ExcelResult
                  value={item.excel_result_available}
                />
              )}
            {judgeEmptyJson(item?.topN)
              && (
                <TopN
                  value={item.topN}
                  onChange={onHandleVisualizationChange}
                  onComponentChange={onComponentChange}
                />
              )}
            {judgeEmptyJson(item?.map)
              && (
                <div className="w-full h-[500px]">
                  <ChinaMap
                    value={item.map}
                    onChange={onHandleVisualizationChange}
                    onComponentChange={onComponentChange}
                  />
                </div>
              )}
            {/* <SqlResult value='{"rows":{"asdaadsadasd":100,"dsadasdasdsada":2000,"dasdasdas":1000}}' /> */}
          </div>
        </div>
      </Typography>
    )
  }, [item, onComponentChange, onHandleVisualizationChange, onTimeChange])

  const bubbleProps = useMemo(() => {
    const isHideAvatar = item?.pre_message_role === item.role
    const roleStyle = item.role === RoleEnum.USER ? fooAvatar : barAvatar
    return {
      placement: item.role === RoleEnum.USER ? 'end' : 'start',
      avatar: {
        icon: <UserOutlined />,
        style: isHideAvatar ? hideAvatar : roleStyle,
      },
      messageRender: renderMarkdown,
      typing,
    } as GetProps<typeof Bubble>
  }, [item?.pre_message_role, item.role, renderMarkdown, typing])

  return (
    <Bubble {...bubbleProps} className="mb-[10px]" />
  )
}

export default BubbleItem
