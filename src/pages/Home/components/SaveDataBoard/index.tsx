import type { FormInstance, ModalProps } from 'antd'
import type { SaveDataBoardInfo } from '../../container'
import homeService from '@/services/homeService'
import useStore from '@/store'
import { CheckOutlined, CloseOutlined } from '@ant-design/icons'
import { useBoolean, usePagination } from 'ahooks'
import { Button, Divider, Form, Input, List, Modal, Skeleton } from 'antd'
import dayjs from 'dayjs'
import { useCallback, useRef, useState } from 'react'
import { Scrollbars } from 'react-custom-scrollbars'
import InfiniteScroll from 'react-infinite-scroll-component'
import { useNavigate } from 'react-router-dom'

interface IProps extends Omit<ModalProps, 'onCancel'> {
  selectItem: SaveDataBoardInfo
  onCancel: () => void
}

function SaveDataBoard(props: IProps) {
  const { selectItem, open, onCancel } = props

  const [selectId, setSelectId] = useState<number | undefined>(undefined)

  const [keyword, setKeyword] = useState<string | undefined>(undefined)

  const [adding, { setTrue: setAddingTrue, setFalse: setAddingFalse }] = useBoolean(false)

  // 新增看板名称
  const [name, setName] = useState<string | undefined>(undefined)

  const formRef = useRef<FormInstance<{ name: string }>>(null)

  const [inputStatus, setInputStatus] = useState<'error' | undefined>(undefined)

  const messageApi = useStore(state => state.messageApi)

  const navigate = useNavigate()

  const [list, setList] = useState<API.Home.BoardListResultItem[]>([])

  const { run, pagination } = usePagination(async (config) => {
    const { current, pageSize, keyword } = config
    const result = await homeService.boardList({
      page: current,
      size: pageSize,
      keyword: keyword ?? undefined,
    })
    return Promise.resolve({
      list: result.boards,
      total: result.pagination.total,
    })
  }, {
    onSuccess: (res, [config]) => {
      const { current } = config
      if (current === 1) {
        setList(res.list)
      }
      else {
        setList((draft) => {
          return [
            ...draft,
            ...res.list,
          ]
        })
      }
    },
    defaultPageSize: 10,
    defaultCurrent: 1,
  })

  const onOk = async () => {
    const value = await formRef.current?.validateFields()
    if (selectId && value?.name) {
      const params = {
        component_name: value?.name,
        board_id: String(selectId),
        ...selectItem,
      } as API.Home.SaveComponentsToBoardParams
      await homeService.saveComponentsToBoard(params)
      messageApi.success('保存成功')
      onCancel?.()
      navigate(`/dataBoardEdit/${selectId}`)
    }
    else {
      messageApi.error('请选择数据看板')
    }
  }

  const onSearch = useCallback(() => {
    run({
      current: 1,
      pageSize: pagination.pageSize,
      keyword,
    })
  }, [keyword, pagination.pageSize, run])

  const onAddPanel = useCallback(async () => {
    if (name) {
      const res = await homeService.createBoard(name)
      const listItem = {
        board_name: res.board_name,
        id: res.id,
        component_count: 0,
        create_time: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        update_time: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      }
      setList((draft) => {
        return [...draft, listItem]
      })
      setName(undefined)
      setAddingFalse()
      messageApi.success('创建成功')
    }
    else {
      messageApi.warning('请输入看板名称')
    }
  }, [messageApi, name, setAddingFalse])

  const onCreatePanel = () => {
    setInputStatus(undefined)
    setName(undefined)
    setAddingTrue()
  }

  const onHandleNext = useCallback(() => {
    const newPage = pagination.current + 1
    run({
      current: newPage,
      pageSize: pagination.pageSize,
    })
  }, [pagination, run])

  return (
    <Modal
      title="保存到数据看板"
      width={540}
      open={open}
      onOk={onOk}
      maskClosable={false}
      classNames={{
        header: '!px-6 !py-4 !border-b !border-[#F0F0F0]',
        content: '!p-0',
        body: '!p-6',
        footer: '!px-4 !py-[10px] !border-t !border-[#F0F0F0] !mt-0',
      }}
      onCancel={onCancel}
    >
      <Form ref={formRef}>
        <Form.Item
          name="name"
          label="名称"
          rules={[
            {
              required: true,
              message: '请输入名称',
            },
          ]}
        >
          <Input className="h-[36px]" placeholder="请输入" />
        </Form.Item>
      </Form>
      <div className="border border-[#D9D9D9] p-4 relative rounded-[6px]">
        <div className="flex justify-between">
          <Button className="!h-[36px]" type="primary" onClick={onCreatePanel}>新增看板</Button>
          <Input.Search
            className="!w-[240px]"
            value={keyword}
            onChange={(e) => {
              setKeyword(e.target.value)
            }}
            onSearch={onSearch}
            placeholder="请输入搜索关键词"
          />
        </div>
        <div className="mt-4 h-[240px]">
          <Scrollbars>
            {adding && (
              <div className="h-[50px] border-b px-[24px] flex items-center gap-x-3 text-[#203FDF] border-[#F0F0F0]">
                <Input
                  status={inputStatus}
                  placeholder="请输入看板名称"
                  value={name}
                  onChange={(e) => {
                    setName(e.target.value)
                    if (e.target.value) {
                      setInputStatus(undefined)
                    }
                    else {
                      setInputStatus('error')
                    }
                  }}
                />
                <CloseOutlined className="cursor-pointer" onClick={setAddingFalse} />
                <CheckOutlined className="cursor-pointer" onClick={onAddPanel} />
              </div>
            )}
            <InfiniteScroll
              dataLength={list.length}
              next={onHandleNext}
              hasMore={list.length < pagination.total}
              loader={<Skeleton avatar paragraph={{ rows: 1 }} active />}
              endMessage={<Divider plain>没有更多了</Divider>}
              scrollableTarget="scrollableDiv"
            >
              <List
                dataSource={list}
                renderItem={(item, index) => (
                  <List.Item key={index}>
                    <div
                      key={index}
                      onClick={() => setSelectId(item.id)}
                      className={`w-full h-[40px] leading-[40px] pl-[16px] pr-[50px] hover:bg-[#F2F3F5] hover:text-[#203FDF] cursor-pointer rounded-[2px] flex items-center justify-between relative ${selectId === item.id && '!bg-[#E4E8FB] text-[#203FDF]'}`}
                    >
                      <div className="w-full truncate">{item.board_name}</div>
                      {selectId === item.id && <CheckOutlined className="!text-[#203FDF] absolute right-6" />}
                    </div>
                  </List.Item>
                )}
              />
            </InfiniteScroll>
          </Scrollbars>
        </div>
      </div>
    </Modal>
  )
}

export default SaveDataBoard
