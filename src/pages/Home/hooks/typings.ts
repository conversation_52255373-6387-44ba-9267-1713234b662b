import type { ComponentTimeGranularityEnum, RoleEnum, TimeTypeEnum } from '@/enum/components/home'

/**
 * 对话子项类型
 */
export interface ConversationDataItem extends API.Home.ConversationDataItem {
  /**
   * 上一条消息的类型
   */
  pre_message_role?: RoleEnum
}

/**
 * 基础消息类型
 */
export interface StreamMessageType {
  event: 'message'
  data: {
    delta: {
      content: string
    }
  }
}

/**
 * 时间选择器类型
 */
export interface StreamTimeInfoType {
  event: 'time_info_extracted'
  data: {
    time_type: TimeTypeEnum
    start_time: string
    end_time: string
    workflow_id: string
    time_granularity: ComponentTimeGranularityEnum
  }
}

/**
 * 可视化数据类型-echarts自主渲染
 */
export interface StreamVisualizationDataType {
  event: 'visualization_data'
  data: {
    chart_type: string
    echarts_option: Record<string, string>
  }
}

/**
 * 表格数据类型（带下载）
 */
export interface StreamExcelDataType {
  event: 'excel_result_available'
  data: {
    workflow_id: string
    data: Record<string, string>[]
    download_url: string
    excel_filename: string
    message: string
  }
}

/**
 * echarts地图类型
 */
export interface StreamEchartsMapDataType {
  event: 'map'
  data: {
    data: Record<string, string>[]
    workflow_id: string
    title: string
  }
}

/**
 * sql查询类型
 */
export interface StreamSqlExecutionResultType {
  event: 'sql_execution_result'
  data: {
    rows: Record<string, string>
  }
}

/**
 * 排名类型
 */
export interface StreamRankDataType {
  event: 'topN'
  data: {
    data: Record<string, any>[]
    title: string
    description: string
    workflow_id: string
  }
}
