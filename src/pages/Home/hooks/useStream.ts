import type { ConversationDataItem } from './typings'
import { useCallback } from 'react'

interface IProps {
  setConversationData: React.Dispatch<React.SetStateAction<ConversationDataItem[]>>
}

function useStream(props: IProps) {
  const { setConversationData } = props

  const SETSTATE = useCallback((params: {
    id: string
    property: string
    value: any
  }) => {
    const { id, property, value } = params
    setConversationData((draft) => {
      return draft.map((item) => {
        if (item.id === id) {
          return { ...item, [property]: value }
        }
        return item
      })
    })
  }, [setConversationData])

  const streamSuccess = useCallback((messages: any[], id: string) => {
    const timeInfo = messages.find(item => item.event.trim() === 'time_info_extracted')

    // 时间选择器
    if (timeInfo?.data) {
      const data = timeInfo?.data
      SETSTATE({
        id,
        property: 'time_info_extracted',
        value: data,
      })
    }

    // 可视化数据
    const visualizationData = messages.find(item => item.event.trim() === 'visualization_data')
    if (visualizationData?.data) {
      const data = visualizationData?.data
      SETSTATE({
        id,
        property: 'visualization_data',
        value: data,
      })
    }

    // 表格数据
    const excelData = messages.find(item => item.event.trim() === 'excel_result_available')
    if (excelData?.data) {
      const data = excelData?.data
      SETSTATE({
        id,
        property: 'excel_result_available',
        value: data,
      })
    }

    // 地图数据
    const mapData = messages.find(item => item.event.trim() === 'map')
    if (mapData?.data) {
      const data = mapData?.data
      SETSTATE({
        id,
        property: 'map',
        value: data,
      })
    }

    // 排名数据
    const topNData = messages.find(item => item.event.trim() === 'topN')
    if (topNData?.data) {
      const data = topNData?.data
      SETSTATE({
        id,
        property: 'topN',
        value: data,
      })
    }

    // sql查询数据
    const sqlData = messages.find(item => item.event.trim() === 'sql_execution_result')
    if (sqlData?.data) {
      const data = sqlData?.data
      SETSTATE({
        id,
        property: 'sql_execution_result',
        value: data,
      })
    }
  }, [SETSTATE])

  return {
    streamSuccess,
  }
}

export default useStream
