import userService from '@/services/userService'
import useStore from '@/store'
import { useRequest } from 'ahooks'
import { Button, Input } from 'antd'
import { useState } from 'react'
import { useNavigate } from 'react-router-dom'

function Login() {
  const [username, setUsername] = useState('')
  const [password, setPassword] = useState('')

  const navigate = useNavigate()

  const { setToken, setTokenType, getUserInfo } = useStore()

  const { run, loading } = useRequest(async () => await userService.login({
    username,
    password,
    grant_type: 'password',
  }), {
    onSuccess: (data) => {
      setToken(data.access_token)
      setTokenType(data.token_type)
      getUserInfo(() => {
        navigate('/home')
      })
    },
    manual: true,
  })

  return (
    <div className="size-full flex justify-center items-center">
      <div className="w-[500px] h-[450px] m-[0_auto] flex flex-col items-center justify-center gap-[30px] shadow-2xl rounded-[8px] p-[24px]">
        <div className="text-[24px] font-bold">
          和信 ChatBI
        </div>
        <div className="flex flex-col gap-[16px] w-full">
          <Input placeholder="请输入用户名" id="username" className="h-[40px]" value={username} onChange={e => setUsername(e.target.value)} />
          <Input placeholder="请输入密码" type="password" id="password" className="h-[40px]" value={password} onChange={e => setPassword(e.target.value)} />
        </div>
        <Button type="primary" className="w-[150px]" onClick={() => run()} loading={loading}>
          登录
        </Button>
      </div>
    </div>
  )
}

export default Login
