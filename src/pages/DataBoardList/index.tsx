import dataBoardBg from '@/assets/images/dataBoardBg.png'
import homeService from '@/services/homeService'
import { PlusOutlined } from '@ant-design/icons'
import { useMount, usePagination, useRequest } from 'ahooks'
import { Button, Input, message, Modal, Pagination, Spin } from 'antd'
import { useState } from 'react'
import ReactCustomScrollbars from 'react-custom-scrollbars'
import { useNavigate } from 'react-router-dom'

function DataBoardList() {
  const navigate = useNavigate()

  const [keyword, setKeyword] = useState('')

  const { data, run, pagination } = usePagination(async (config) => {
    const { current, pageSize, keyword } = config
    const result = await homeService.boardList({
      page: current,
      size: pageSize,
      keyword: keyword ?? undefined,
    })
    return Promise.resolve({
      list: result.boards,
      total: result.pagination.total,
    })
  }, {
    defaultPageSize: 10,
    defaultCurrent: 1,
  })

  const { loading, run: onCreate } = useRequest(async () => {
    const result = await homeService.createBoard('未命名数据看板')
    return result
  }, {
    onSuccess: (result) => {
      navigate(`/dataBoardEdit/${result.id}`)
    },
    manual: true,
  })

  const { run: onDeleteRun } = useRequest(async (id: number) => await homeService.deleteBoard(id), {
    manual: true,
    onSuccess: () => {
      message.success('删除成功')
      run({
        current: 1,
        pageSize: 10,
        keyword,
      })
    },
  })

  const onDelete = (id: number) => {
    Modal.confirm({
      title: '您确定要删除该数据看板吗？删除操作无法撤销',
      width: 460,
      onOk: () => {
        onDeleteRun(id)
      },
    })
  }

  const onEdit = (id: number) => {
    navigate(`/dataBoardEdit/${id}`)
  }

  return (
    <div className="h-full flex flex-col">
      <div className="text-[#1D2129] flex justify-between items-center text-[20px] font-bold mb-5 bg-white px-[16px] py-[12px] rounded-[4px]">
        <span>数据看板</span>
        <Input.Search
          placeholder="搜索"
          size="large"
          value={keyword}
          onChange={e => setKeyword(e.target.value)}
          onSearch={() => {
            run({
              current: 1,
              pageSize: 10,
              keyword,
            })
          }}
          className="!w-[300px] ml-auto"
        />
      </div>
      <div className="flex-1 bg-white p-[16px] rounded-[4px]">
        <ReactCustomScrollbars>
          <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-5">
            <div onClick={onCreate} className="w-full h-full min-h-[245px] cursor-pointer text-[#86909C] font-bold justify-center items-center border border-[#E5E8EF] rounded-[4px] transition-shadow hover:shadow-[4px_4px_10px_0px_rgba(0,0,0,0.06)]">
              <Spin spinning={loading} wrapperClassName="w-full h-full text-center flex items-center justify-center">
                <PlusOutlined className="text-[22px]" />
                <div className="mt-3">新建数据看板</div>
              </Spin>
            </div>
            {data?.list?.map(item => (
              <div key={item.id} className="w-full bg-white rounded-[4px] border border-[#E5E8EF] transition-shadow cursor-pointer group hover:shadow-[4px_4px_10px_0px_rgba(0,0,0,0.06)]">
                <div className="w-full relative">
                  <img className="w-full object-cover aspect-[330/182]" src={dataBoardBg} alt="bg" />
                  <div className="absolute inset-0 bg-[rgba(44,44,44,0.35)] opacity-0 group-hover:opacity-100 flex items-center justify-center gap-4 transition-opacity">
                    <Button type="primary" onClick={() => navigate(`/dataBoardEdit/${item.id}`)}>查看</Button>
                    <Button onClick={() => onEdit(item.id)}>编辑</Button>
                    <Button danger onClick={() => onDelete(item.id)}>删除</Button>
                  </div>
                </div>
                <div className="h-[54px] text-[#1D2129] text-[15px] font-bold flex items-center px-4">
                  <span className="flex-1 truncate" title={item.board_name}>{item.board_name}</span>
                  <span className="rounded-full size-[26px] text-[12px] flex justify-center items-center text-white bg-[#203FDF]">和</span>
                </div>
              </div>
            ))}
          </div>
        </ReactCustomScrollbars>
      </div>
      <div className="bg-white rounded-[4px] px-[16px] py-[12px]">
        <Pagination
          className="flex justify-end mt-16px"
          showQuickJumper
          showSizeChanger
          showTotal={(total: number) => (
            <span>
              共
              {' '}
              {total}
              {' '}
              条记录
            </span>
          )}
          {...pagination}
        />
      </div>
    </div>
  )
}
export default DataBoardList
