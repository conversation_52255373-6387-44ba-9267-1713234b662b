import homeService from '@/services/homeService'
import { EditOutlined } from '@ant-design/icons'
import { useBoolean } from 'ahooks'
import { Button, Input, message, Space } from 'antd'
import React, { useCallback, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import Container from '../../container'

function Header() {
  const [isEditing, { setTrue, setFalse }] = useBoolean(false)

  const { title, setTitle, list, id } = Container.useContainer()

  const navigate = useNavigate()

  const [saveLoading, setSaveLoading] = useState(false)

  // 修改标题完成
  const onEditNameFinish = (e: React.FocusEvent<HTMLInputElement> | React.KeyboardEvent<HTMLInputElement>) => {
    const value = (e.target as HTMLInputElement).value
    setTitle(value)
    homeService.renameBoard({
      board_id: id!,
      new_name: value,
    })
    setFalse()
  }

  const onAdd = () => {
    // 跳转页面
    navigate('/home')
  }

  const onSave = useCallback(() => {
    setSaveLoading(true)
    homeService.editBoardComponents({
      board_id: id!,
      components: list,
    })
    message.success('保存成功')
    setSaveLoading(false)
  }, [id, list])

  const onBack = () => {
    navigate(-1) // 返回上一页
  }

  return (
    <div className="h-[60px] bg-white w-full flex items-center justify-between px-[30px]">
      <Button onClick={onBack}>返回</Button>
      <div className="w-[300px] text-[18px] font-bold flex justify-center">
        {isEditing && (
          <Input
            defaultValue={title}
            autoFocus
            onBlur={onEditNameFinish}
            onPressEnter={onEditNameFinish}
            className="flex-1 !text-[18px] !font-bold"
          />
        )}
        {!isEditing && (
          <React.Fragment>
            <div className="truncate px-3" title={title}>{title}</div>
            <EditOutlined className="cursor-pointer" onClick={setTrue} />
          </React.Fragment>
        )}
      </div>
      <Space>
        <Button type="primary" onClick={onAdd}>新增组件</Button>
        <Button type="primary" loading={saveLoading} onClick={onSave}>保存</Button>
      </Space>
    </div>
  )
}
export default Header
