import type { FormInstance } from 'antd'
import type { Dayjs } from 'dayjs'
import { ComponentTimeTypeEnum, ComponentTypeEnum } from '@/enum'
import Container from '@/pages/DataBoardEdit/container'
import MapComp from '@/pages/Home/components/Content/components/BubbleItem/components/Map'
import TopNComp from '@/pages/Home/components/Content/components/BubbleItem/components/TopN'
import VisualizationComp from '@/pages/Home/components/Content/components/BubbleItem/components/Visualization'
import homeService from '@/services/homeService'
import { CloseOutlined, EditOutlined } from '@ant-design/icons'
import { useBoolean } from 'ahooks'
import { Button, Card, DatePicker, Form, Input } from 'antd'
import dayjs from 'dayjs'
import React, { useCallback, useEffect, useRef, useState } from 'react'
import styles from './index.module.less'

interface IProps {
  item: API.Home.ComponentsItem
  removeItem: (id: string) => void
  renameItem: (id: string, name: string) => void
}

interface FormRefType {
  time: Dayjs
  timeRang: Dayjs[]
}

function ItemCard(props: IProps) {
  const { item, removeItem, renameItem } = props

  const [title, setTitle] = useState(item.name)

  const [isEditing, { setTrue, setFalse }] = useBoolean(false)

  const formRef = useRef<FormInstance<FormRefType>>(null)

  const [loading, setLoading] = useState(false)

  const { updateItemCode } = Container.useContainer()

  // 修改标题完成
  const onEditNameFinish = useCallback((e: React.FocusEvent<HTMLInputElement> | React.KeyboardEvent<HTMLInputElement>) => {
    const value = (e.target as HTMLInputElement).value
    if (value === item.name) {
      setFalse()
      return
    }
    setTitle(value)
    homeService.renameBoardComponent({
      component_id: item.id,
      new_name: value,
    })
    renameItem(item.id, value)
    setFalse()
  }, [item.id, item.name, renameItem, setFalse])

  const getFormatFormValue = useCallback(() => {
    const formValues = formRef.current?.getFieldsValue()
    if (formValues) {
      return {
        start_time: item.time_type === ComponentTimeTypeEnum.Range ? formValues?.timeRang[0]?.format('YYYY-MM-DD HH:mm:ss') : '',
        stop_time: item.time_type === ComponentTimeTypeEnum.Range ? formValues?.timeRang[1]?.format('YYYY-MM-DD HH:mm:ss') : formValues?.time?.format('YYYY-MM-DD HH:mm:ss'),
      }
    }
    return false
  }, [item.time_type])

  const onSearch = useCallback(async () => {
    const values = getFormatFormValue()
    if (!values) {
      return
    }
    setLoading(true)
    const params = {
      workflow_id: item.workflow_id,
      new_time_end: values.stop_time,
      new_time_start: values.start_time,
    }
    const res = await homeService.getTimeSelect(params)
    // 根据类型获取code
    switch (item.component_type) {
      case ComponentTypeEnum.TopN:
        updateItemCode(item.id, res.topN)
        break
      case ComponentTypeEnum.Echarts:
        updateItemCode(item.id, res.visualization_data)
        break
      case ComponentTypeEnum.Map:
        updateItemCode(item.id, res.map)
        break
    }
    setLoading(false)
  }, [getFormatFormValue, item.component_type, item.id, item.workflow_id, updateItemCode])

  useEffect(() => {
    if (item.time_type === ComponentTimeTypeEnum.Range) {
      formRef.current?.setFieldsValue({
        timeRang: [dayjs(item.start_time), dayjs(item.stop_time)],
      })
    }
    if (item.time_type === ComponentTimeTypeEnum.Point) {
      formRef.current?.setFieldsValue({
        time: dayjs(item.stop_time),
      })
    }
  }, [item.start_time, item.stop_time, item.time_type])

  return (
    <React.Fragment>
      <div className={styles.itemCard}>
        <Card
          variant="borderless"
          title={(
            <div className="w-full flex items-center">
              {isEditing && (
                <Input
                  defaultValue={item.name}
                  autoFocus
                  onBlur={onEditNameFinish}
                  onPressEnter={onEditNameFinish}
                  className="flex-1 !text-[18px] !font-bold"
                />
              )}
              {!isEditing && (
                <React.Fragment>
                  <div className="truncate px-3" title={title}>{title}</div>
                  <EditOutlined
                    className="cursor-pointer"
                    onMouseDown={event => event.stopPropagation()}
                    onClick={() => {
                      setTrue()
                    }}
                  />
                </React.Fragment>
              )}
            </div>
          )}
          className="w-full h-full"
          extra={
            <CloseOutlined onClick={() => removeItem(item.id)} className="!text-[rgba(0,0,0,0.25)] text-[17px] cursor-pointer no-drag" />
          }
        >
          <div
            className="flex items-center gap-[8px] justify-center"
            onMouseDown={event => event.stopPropagation()}
          >
            <div className="flex-1">
              <Form ref={formRef} layout="inline">
                {
                  item.time_type === ComponentTimeTypeEnum.Range && (
                    <Form.Item name="timeRang" className="w-full">
                      <DatePicker.RangePicker className="w-full" showTime format="YYYY-MM-DD HH:mm:ss" allowClear={false} />
                    </Form.Item>
                  )
                }
                {
                  item.time_type === ComponentTimeTypeEnum.Point && (
                    <Form.Item name="time" className="w-full">
                      <DatePicker className="w-full" showTime format="YYYY-MM-DD HH:mm:ss" allowClear={false} />
                    </Form.Item>
                  )
                }
              </Form>
            </div>
            <div className="w-[80px]">
              <Button type="primary" onClick={() => onSearch()} loading={loading}>查询</Button>
            </div>
          </div>
          <div className="h-[calc(100%-48px)]">
            {
              item.component_type === ComponentTypeEnum.TopN && <TopNComp value={item.code} toolbar={false} />
            }
            {
              item.component_type === ComponentTypeEnum.Echarts && <VisualizationComp value={item.code} toolbar={false} />
            }
            {
              item.component_type === ComponentTypeEnum.Map && <MapComp value={item.code} toolbar={false} />
            }
          </div>
        </Card>
      </div>
    </React.Fragment>
  )
}

export default ItemCard
