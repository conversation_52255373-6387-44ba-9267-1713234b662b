import { Spin } from 'antd'
import { Scrollbars } from 'react-custom-scrollbars'
import GridLayout, { WidthProvider } from 'react-grid-layout'
import Container from '../../container'
import ItemCard from './components/ItemCard'
import 'react-grid-layout/css/styles.css'

const ResponsiveReactGridLayout = WidthProvider(GridLayout)

function List() {
  const { list, layout, onLayoutChange, loading, removeItem, renameItem } = Container.useContainer()

  return (
    <div
      className="flex-1 bg-[length:20px_20px]"
      style={{
        backgroundImage:
          'linear-gradient(to right, #e5e7eb 1px, transparent 1px), linear-gradient(to bottom, #e5e7eb 1px, transparent 1px)',
      }}
    >
      <Spin className="w-full top-[160px] absolute z-100" spinning={loading} />
      <Scrollbars>
        <ResponsiveReactGridLayout
          layout={layout}
          rowHeight={24}
          cols={12}
          margin={[16, 16]}
          containerPadding={[16, 16]}
          onLayoutChange={onLayoutChange}
          autoSize
          isResizable
          // preventCollision
          // resizeHandles={['e', 's', 'se', 'sw', 'w', 'n', 'ne', 'nw']}
          isDraggable
          useCSSTransforms
          draggableCancel=".no-drag"
        >
          {list.map(val => (
            <div key={val.id}>
              <ItemCard item={val} removeItem={removeItem} renameItem={renameItem} />
            </div>
          ))}
        </ResponsiveReactGridLayout>
      </Scrollbars>
    </div>
  )
}
export default List
