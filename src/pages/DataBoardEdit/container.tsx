import type { Layout } from 'react-grid-layout'
import homeService from '@/services/homeService'
import { safeJsonParse } from '@/utils'
import { useRequest } from 'ahooks'
import { message, Modal } from 'antd'
import { useCallback, useMemo, useState } from 'react'
import { useParams } from 'react-router-dom'
import { createContainer } from 'unstated-next'

interface ListItem extends API.Home.ComponentsItem {
  gridConfig: Omit<Layout, 'i'>
}
function useContainer() {
  const [title, setTitle] = useState('未命名看板')

  const { id } = useParams()

  const [list, setList] = useState<ListItem[]>([])

  const { loading } = useRequest(async () => {
    return await homeService.getBoardDetail(id!)
  }, {
    onSuccess: (res) => {
      setTitle(res.name)
      const components = res.components?.map((item) => {
        const json = safeJsonParse(item.json, {})
        return {
          ...item,
          gridConfig: {
            x: json.x,
            y: json.y,
            w: json.w,
            h: json.h,
          },
        }
      })
      setList(components)
    },
  })

  const layout = useMemo(() => {
    return list.map((val) => {
      return {
        i: val.id,
        ...val.gridConfig,
      }
    })
  }, [list])

  const onLayoutChange = (layout: Layout[]) => {
    const newList = list.map((val, index) => {
      const newGridConfig = layout[index]
      const gridConfig = {
        x: newGridConfig?.x,
        y: newGridConfig?.y,
        w: newGridConfig?.w,
        h: newGridConfig?.h,
      }
      return {
        ...val,
        json: JSON.stringify(gridConfig),
        gridConfig,
      }
    })
    setList(newList)
  }

  const removeItem = (id: string) => {
    // 是否确认删除
    Modal.confirm({
      title: '是否确认删除',
      onOk: async () => {
        await homeService.deleteBoardComponent(id)
        message.success('删除成功')
        const newList = list.filter(item => item.id !== id)
        setList(newList)
      },
    })
  }

  const renameItem = (id: string, name: string) => {
    const newList = list.map(item => item.id === id ? { ...item, name } : item)
    setList(newList)
  }

  // 修改列表子项中的code
  const updateItemCode = useCallback((id: string, code?: string) => {
    if (!code) {
      return
    }
    const newList = list.map(item => item.id === id ? { ...item, code } : item)
    setList(newList)
  }, [list])

  return {
    title,
    setTitle,
    list,
    layout,
    onLayoutChange,
    removeItem,
    loading,
    id,
    renameItem,
    updateItemCode,
  }
}

const Container = createContainer(useContainer)

export type ContainerType = typeof useContainer

export { useContainer }

export default Container
