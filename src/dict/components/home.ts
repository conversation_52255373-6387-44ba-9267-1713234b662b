import { ComponentTimeGranularityEnum, ComponentTimeGranularityProEnum, ComponentTimeTypeEnum, GridColSpanEnum, TimeTypeEnum, TimeUnitEnum } from '@/enum/components/home'
import dayjs from 'dayjs'

const dictInfo = {
  timeUnit: [
    {
      label: '小时',
      value: TimeUnitEnum.Hour,
      format: 'HH:mm:ss',
    },
    {
      label: '天',
      value: TimeUnitEnum.Day,
      format: 'YYYY-MM-DD',
    },
    {
      label: '月',
      value: TimeUnitEnum.Month,
      format: 'YYYY-MM',
    },
    {
      label: '年',
      value: TimeUnitEnum.Year,
      format: 'YYYY',
    },
  ],
  gridColSpan: [
    {
      label: '1/4',
      value: GridColSpanEnum.Col1,
    },
    {
      label: '1/2',
      value: GridColSpanEnum.Col2,
    },
    {
      label: '1/3',
      value: GridColSpanEnum.Col3,
    },
    {
      label: '1/4',
      value: GridColSpanEnum.Col4,
    },
  ],
  componentDayTimeGranularity: [
    {
      label: '今日',
      value: ComponentTimeGranularityProEnum.Today,
      start: dayjs().startOf('day').valueOf(),
      end: dayjs().endOf('day').valueOf(),
    },
    {
      label: '昨日',
      value: ComponentTimeGranularityProEnum.Yesterday,
      start: dayjs().subtract(1, 'day').startOf('day').valueOf(),
      end: dayjs().subtract(1, 'day').endOf('day').valueOf(),
    },
    {
      label: '本周',
      value: ComponentTimeGranularityProEnum.ThisWeek,
      start: dayjs().startOf('week').valueOf(),
      end: dayjs().endOf('week').valueOf(),
    },
    {
      label: '上周',
      value: ComponentTimeGranularityProEnum.LastWeek,
      start: dayjs().subtract(1, 'week').startOf('week').valueOf(),
      end: dayjs().subtract(1, 'week').endOf('week').valueOf(),
    },
    {
      label: '本月',
      value: ComponentTimeGranularityProEnum.ThisMonth,
      start: dayjs().startOf('month').valueOf(),
      end: dayjs().endOf('month').valueOf(),
    },
    {
      label: '上月',
      value: ComponentTimeGranularityProEnum.LastMonth,
      start: dayjs().subtract(1, 'month').startOf('month').valueOf(),
      end: dayjs().subtract(1, 'month').endOf('month').valueOf(),
    },
    {
      label: '今年',
      value: ComponentTimeGranularityProEnum.ThisYear,
      start: dayjs().startOf('year').valueOf(),
      end: dayjs().endOf('year').valueOf(),
    },
  ],
  componentMonthTimeGranularity: [
    {
      label: '本月',
      value: ComponentTimeGranularityProEnum.ThisMonth,
      start: dayjs().startOf('month').valueOf(),
      end: dayjs().endOf('month').valueOf(),
    },
    {
      label: '上月',
      value: ComponentTimeGranularityProEnum.LastMonth,
      start: dayjs().subtract(1, 'month').startOf('month').valueOf(),
      end: dayjs().subtract(1, 'month').endOf('month').valueOf(),
    },
    {
      label: '近三月',
      value: ComponentTimeGranularityProEnum.LastThreeMonths,
      start: dayjs().subtract(3, 'month').startOf('month').valueOf(),
      end: dayjs().subtract(3, 'month').endOf('month').valueOf(),
    },
    {
      label: '近六个月',
      value: ComponentTimeGranularityProEnum.LastSixMonths,
      start: dayjs().subtract(6, 'month').startOf('month').valueOf(),
      end: dayjs().subtract(6, 'month').endOf('month').valueOf(),
    },
    {
      label: '近一年',
      value: ComponentTimeGranularityProEnum.LastYear,
      start: dayjs().subtract(1, 'year').startOf('year').valueOf(),
      end: dayjs().subtract(1, 'year').endOf('year').valueOf(),
    },
  ],
  componentHourTimeGranularity: [
    {
      label: '近1小时',
      value: ComponentTimeGranularityProEnum.LastOneHour,
      start: dayjs().subtract(1, 'hour').startOf('hour').valueOf(),
      end: dayjs().subtract(1, 'hour').endOf('hour').valueOf(),
    },
    {
      label: '今日',
      value: ComponentTimeGranularityProEnum.Today,
      start: dayjs().startOf('day').valueOf(),
      end: dayjs().endOf('day').valueOf(),
    },
    {
      label: '近24小时',
      value: ComponentTimeGranularityProEnum.Last24Hours,
      start: dayjs().subtract(24, 'hour').startOf('hour').valueOf(),
      end: dayjs().subtract(24, 'hour').endOf('hour').valueOf(),
    },
  ],
  componentYearTimeGranularity: [
    {
      label: '今年',
      value: ComponentTimeGranularityProEnum.ThisYear,
      start: dayjs().startOf('year').valueOf(),
      end: dayjs().endOf('year').valueOf(),
    },
    {
      label: '去年',
      value: ComponentTimeGranularityProEnum.LastYear,
      start: dayjs().subtract(1, 'year').startOf('year').valueOf(),
      end: dayjs().subtract(1, 'year').endOf('year').valueOf(),
    },
    {
      label: '近三年',
      value: ComponentTimeGranularityProEnum.LastThreeYears,
      start: dayjs().subtract(3, 'year').startOf('year').valueOf(),
      end: dayjs().subtract(3, 'year').endOf('year').valueOf(),
    },
  ],
  componentTimeGranularityToTimeUnit: {
    [ComponentTimeGranularityEnum.Hour]: TimeUnitEnum.Hour,
    [ComponentTimeGranularityEnum.Day]: TimeUnitEnum.Day,
    [ComponentTimeGranularityEnum.Month]: TimeUnitEnum.Month,
    [ComponentTimeGranularityEnum.Year]: TimeUnitEnum.Year,
  },
  timeUnitToComponentTimeGranularity: {
    [TimeUnitEnum.Hour]: ComponentTimeGranularityEnum.Hour,
    [TimeUnitEnum.Day]: ComponentTimeGranularityEnum.Day,
    [TimeUnitEnum.Month]: ComponentTimeGranularityEnum.Month,
    [TimeUnitEnum.Year]: ComponentTimeGranularityEnum.Year,
  },
  timeTypeToComponentTimeType: {
    [TimeTypeEnum.Range]: ComponentTimeTypeEnum.Range,
    [TimeTypeEnum.Point]: ComponentTimeTypeEnum.Point,
  },
}

export default dictInfo
