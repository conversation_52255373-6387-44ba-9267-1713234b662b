import request from '@/utils/fetch'

class HomeService {
  getConversations(params: API.Home.GetConversationsParams) {
    return request<API.Home.GetConversationsResult>({
      method: 'get',
      url: '/session/getConversations',
      name: '获取对话列表',
      params,
    })
  }

  getConversationById(id: string) {
    return request<API.Home.ConversationDataResult>({
      method: 'get',
      url: `/session/getConversationById/${id}`,
      name: '获取对话详情',
    })
  }

  getSuggestedQuestions(params: API.Home.GetSuggestedQuestionsParams) {
    return request<API.Home.GetSuggestedQuestionsResult>({
      method: 'post',
      url: '/messages/suggested-questions',
      name: '建议问题',
      params,
    })
  }

  createConversation() {
    return request<API.Home.CreateConversationResult>({
      method: 'post',
      url: '/session/conversation/create',
      name: '创建对话',
    })
  }

  chat(params: API.Home.ChatParams) {
    return request<any>({
      method: 'post',
      url: '/chat/stream',
      name: '对话',
      params,
    })
  }

  getTimeSelect(params: API.Home.GetTimeSelectParams) {
    return request<API.Home.GetTimeSelectResult>({
      method: 'post',
      url: '/time-selection/change',
      name: '获取时间选择',
      params,
    })
  }

  deleteConversation(id: string) {
    return request<API.Success>({
      method: 'post',
      url: '/session/deleteConversationById',
      name: '删除对话',
      params: {
        id,
      },
    })
  }

  deleteAllConversations() {
    return request<API.Success>({
      method: 'delete',
      url: '/session/conversations/deleteAllByUserId',
      name: '删除所有对话',
    })
  }

  // 看板相关接口

  createBoard(name: string) {
    return request<API.Home.CreateBoardResult>({
      method: 'post',
      url: '/data-board/create',
      name: '创建数据面板',
      params: {
        name,
      },
    })
  }

  boardList(params: API.Home.BoardListParams) {
    return request<API.Home.BoardListResult>({
      method: 'get',
      url: '/data-board/list',
      name: '获取数据面板列表',
      params,
    })
  }

  saveComponentsToBoard(params: API.Home.SaveComponentsToBoardParams) {
    return request<API.Success>({
      method: 'post',
      url: '/data-board/component/save',
      name: '保存组件到数据面板',
      params,
    })
  }

  getBoardDetail(id: string) {
    return request<API.Home.BoardDetailResult>({
      method: 'get',
      url: `/data-board/${id}`,
      name: '获取数据面板详情',
    })
  }

  editBoardComponents(params: API.Home.EditBoardComponentsParams) {
    return request<API.Success>({
      method: 'put',
      url: '/data-board/edit',
      name: '编辑数据面板',
      params,
    })
  }

  renameBoard(params: API.Home.RenameBoardParams) {
    return request<API.Success>({
      method: 'put',
      url: `/data-board/rename`,
      name: '重命名数据面板',
      params,
    })
  }

  renameBoardComponent(params: API.Home.RenameBoardComponentParams) {
    return request<API.Success>({
      method: 'put',
      url: `/data-board/component/rename`,
      name: '重命名组件',
      params,
    })
  }

  deleteBoard(id: number) {
    return request<API.Success>({
      method: 'delete',
      url: `/data-boarddelete/${id}`,
      name: '删除数据面板',
    })
  }

  deleteBoardComponent(id: string) {
    return request<API.Success>({
      method: 'delete',
      url: `/data-boarddelete/component/${id}`,
      name: '删除组件',
    })
  }

  // 新看板下相关接口
  getBoardList(params: API.Home.GetBoardListParams) {
    return request<API.Home.GetBoardListResult>({
      method: 'get',
      url: '/data-board/component/list',
      name: '获取数据面板列表',
      params,
    })
  }

  saveComponent(params: API.Home.SaveComponentParams) {
    return request<API.Success>({
      method: 'post',
      url: '/data-board/save-chart',
      name: '保存组件',
      params,
    })
  }

  deleteComponent(id: string) {
    return request<API.Success>({
      method: 'delete',
      url: `/data-board/component/sync-delete/${id}`,
      name: '删除组件',
    })
  }

  toppingComponent(params: API.Home.ToppingComponentParams) {
    return request<API.Success>({
      method: 'put',
      url: '/data-board/component/topping-status',
      name: '置顶组件',
      params,
    })
  }

  moveUpComponent(component_id: string) {
    return request<API.Success>({
      method: 'put',
      url: '/data-board/component/move-up',
      name: '上移组件',
      params: {
        component_id,
      },
    })
  }

  moveDownComponent(component_id: string) {
    return request<API.Success>({
      method: 'put',
      url: '/data-board/component/move-down',
      name: '下移组件',
      params: {
        component_id,
      },
    })
  }
}

export default new HomeService()
