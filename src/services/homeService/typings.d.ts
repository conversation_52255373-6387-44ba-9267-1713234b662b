import type { ComponentTimeGranularityEnum, ComponentTimeTypeEnum, ComponentTypeEnum, RoleEnum } from '@/enum/components/home'

export namespace HomeType {

  export interface ChatParams {
    /**
     * 消息列表
     */
    messages: {
      role: RoleEnum
      content: string
    }[]

    /**
     * 会话id
     */
    conversationId?: string
  }

  export interface GetConversationsParams {
    /**
     * 页码
     */
    page: number

    /**
     * 每页条数
     */
    size: number
  }

  export interface ConversationsItem extends API.User.ConversationsItem { }

  export interface GetConversationsResult {
    /**
     * 会话列表
     */
    data: ConversationsItem[]

    /**
     * 分页信息
     */
    page_info: {
      /**
       * 是否还有更多
       */
      has_more: boolean

      /**
       * 页码
       */
      page: number

      /**
       * 每页条数
       */
      size: number

      /**
       * 总条数
       */
      total: number
    }
  }

  export interface ConversationDataResult {
    /**
     * 名称
     */
    name: string

    /**
     * 会话id
     */
    id: string

    /**
     * 消息列表
     */
    messages: ConversationDataItem[]
  }

  export interface GetSuggestedQuestionsParams {
    /**
     * 消息id
     */
    message_id: number

    /**
     * 消息限制
     */
    message_limit: number

    /**
     * 最大token限制
     */
    max_token_limit: 3000
  }

  export interface GetSuggestedQuestionsResult {
    /**
     * 建议问题
     */
    questions: string[]
  }

  export interface ConversationDataItem {
    /**
     * 消息id
     */
    id: string

    /**
     * 会话id
     */
    conversation_id: string

    /**
     * 工作流id
     */
    workflow_id: string

    /**
     * 消息内容
     */
    content: string

    /**
     * 消息拥有者
     */
    role: RoleEnum

    /**
     * 时间选择器相关信息
     */
    time_info_extracted?: string

    /**
     * 可视化数据
     */
    visualization_data?: string

    /**
     * 表格数据
     */
    excel_result_available?: string

    /**
     * sql查询数据
     */
    sql_execution_result?: string

    /**
     * 地图数据
     */
    map?: string

    /**
     * 排名数据
     */
    topN?: string
  }

  export interface CreateConversationResult extends ConversationsItem { }

  export interface GetTimeSelectParams {
    /**
     * 工作流id
     */
    workflow_id: string

    /**
     * 新选择开始的时间
     */
    new_time_start?: string

    /**
     * 新选择结束的时间
     */
    new_time_end: string
  }

  export interface GetTimeSelectResult extends Pick<ConversationDataItem, 'visualization_data' | 'sql_execution_result' | 'time_info_extracted' | 'map' | 'topN' | 'excel_result_available'> {

  }

  export interface BoardListParams {
    /**
     * 页码
     */
    page: number

    /**
     * 每页条数
     */
    size: number

    /**
     * 关键词
     */
    keyword?: string
  }

  export interface BoardListResultItem {
    /**
     * 名称
     */
    board_name: string

    /**
     * 组件数量
     */
    component_count: number

    /**
     * 创建时间
     */
    create_time: string

    /**
     * 更新时间
     */
    update_time: string

    /**
     * id
     */
    id: number
  }

  export interface BoardListResult {
    /**
     * 面板数据
     */
    boards: BoardListResultItem[]

    /**
     * 分页信息
     */
    pagination: {
      /**
       * 总数
       */
      total: number
    }
  }

  export interface SaveComponentsToBoardParams {
    /**
     * 销售趋势图
     */
    component_name: string

    /**
     * 面板id
     */
    board_id: string

    /**
     * 对话id
     */
    workflow_id: string

    /**
     * 组件信息
     */
    component_info: {
      /**
       * 组件类型
       */
      component_type: ComponentTypeEnum

      /**
       * 组件相关配置
       */
      json: string

      /**
       * 开始时间
       */
      start_time: string

      /**
       * 结束时间
       */
      stop_time: string

      /**
       * 时间粒度
       */
      time_granularity: ComponentTimeGranularityEnum

      /**
       * 时间类型
       */
      time_type: ComponentTimeTypeEnum
    }
  }
  export interface CreateBoardResult {
    /**
     * 看板名称
     */
    board_name: string

    /**
     * 看板id
     */
    id: number
  }

  export interface ComponentsItem {
    /**
     * code
     */
    code: string

    /**
     * 图表类型
     */
    component_type: ComponentTypeEnum

    /**
     * id
     */
    id: string

    /**
     * 组件配置
     */
    json: string

    /**
     * 名称
     */
    name: string

    /**
     * 开始时间
     */
    start_time: string

    /**
     * 结束时间
     */
    stop_time: string

    /**
     * 时间类型
     */
    time_granularity: ComponentTimeGranularityEnum

    /**
     * 时间类型
     */
    time_type: ComponentTimeTypeEnum

    /**
     * 工作流id
     */
    workflow_id: string
  }

  export interface BoardDetailResult {
    /**
     * id
     */
    id: string

    /**
     * 名称
     */
    name: string

    /**
     * 用户id
     */
    user_id: number

    /**
     * 组件信息
     */
    components: ComponentsItem[]
  }

  export interface EditBoardComponentsParams {
    /**
     * id
     */
    board_id: string

    /**
     * 组件信息
     */
    components: ComponentsItem[]
  }

  export interface RenameBoardParams {
    /**
     * 面板id
     */
    board_id: string

    /**
     * 新名称
     */
    new_name: string
  }

  export interface RenameBoardParams {
    /**
     * 面板id
     */
    board_id: string

    /**
     * 新名称
     */
    new_name: string
  }

  export interface RenameBoardComponentParams {
    /**
     * 组件id
     */
    component_id: string

    /**
     * 新名称
     */
    new_name: string
  }

  export interface GetBoardListParams {
    /**
     * 关键字
     */
    keyword?: string

    /**
     * 页码
     */
    page: number

    /**
     * 每页条数
     */
    size: number
  }

  export interface GetBoardListResultItem extends ComponentsItem {
    /**
     * 组件id
     */
    id: string

    /**
     * 排序
     */
    order_num: number

    /**
     * 是否置顶
     */
    is_topping: '0' | '1'
  }

  export interface GetBoardListResult {
    /**
     * 组件列表
     */
    components: GetBoardListResultItem[]

    /**
     * 分页信息
     */
    pagination: {
      /**
       * 页码
       */
      page: number

      /**
       * 每页条数
       */
      size: number

      /**
       * 总数
       */
      total: number
    }
  }

  export interface SaveComponentParams {
    /**
     * 销售趋势图
     */
    component_name: string

    /**
     * 对话id
     */
    workflow_id: string

    /**
     * 组件id
     */
    component_id?: string

    /**
     * 组件信息
     */
    component_info: Omit<SaveComponentsToBoardParams['component_info'], 'time_granularity'>
  }

  export interface ToppingComponentParams {
    /**
     * 组件id
     */
    component_id: string

    /**
     * 是否置顶
     */
    topping_status: '0' | '1'
  }
}

declare global {
  namespace API {
    export import Home = HomeType
  }
}
