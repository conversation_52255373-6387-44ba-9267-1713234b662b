import request from '@/utils/fetch'

class UserService {
  register(params: API.User.RegisterParams) {
    return request<API.User.RegisterResult>({
      method: 'post',
      url: '/users/register',
      name: '注册',
      params,
    })
  }

  login(data: API.User.LoginParams) {
    return request<API.User.LoginResult>({
      method: 'post',
      url: '/users/login',
      params: data,
      name: '登录',
    })
  }

  detail() {
    return request<API.User.DetailResult>({
      method: 'get',
      url: '/users/detail',
      name: '用户信息',
    })
  }
}

export default new UserService()
