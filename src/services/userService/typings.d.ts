export namespace UserType {
  export interface RegisterParams {
    /**
     * 用户名
     */
    username: string

    /**
     * 密码
     */
    password: string
  }

  export interface RegisterResult {
    /**
     * 用户名称
     */
    username: string

    /**
     * id
     */
    id: string

    /**
     * 状态
     */
    status: string

    /**
     * 最后登录时间
     */
    last_login_time: string

    /**
     * 创建时间
     */
    created_at: string

    /**
     * 更新时间
     */
    updated_at: string
  }

  export interface LoginParams {
    /**
     * 用户名
     */
    username: string

    /**
     * 密码
     */
    password: string

    /**
     * 生成类型
     */
    grant_type: string
  }

  export interface ConversationsItem {
    /**
     * id
     */
    id: string

    /**
     * 会话摘要
     */
    summary: string

    /**
     * 会话内容
     */
    name: string

    /**
     * 会话创建时间
     */
    created_at: string

    /**
     * 会话更新时间
     */
    updated_at: string

    /**
     * 用户id
     */
    user_id: string
  }

  export interface LoginResult {
    /**
     * token
     */
    access_token: string

    /**
     * token类型
     */
    token_type: string
  }

  export interface DetailResult extends RegisterResult {
    /**
     * 最近会话
     */
    conversations: ConversationsItem[]
  }
}

declare global {
  namespace API {
    export import User = UserType
  }
}
