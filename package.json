{"name": "dataanalysis", "type": "module", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "build": "tsc -b && vite build", "build:test": "vite build --mode test", "build:production": "vite build --mode production", "preview": "vite preview", "lint": "eslint", "lint:fix": "eslint --fix"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@ant-design/pro-components": "^2.8.6", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@ant-design/x": "^1.2.0", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@tailwindcss/vite": "^4.0.15", "@turf/turf": "^7.2.0", "@types/geojson": "^7946.0.16", "@types/history": "^5.0.0", "@types/markdown-it": "^14.1.2", "@types/react-grid-layout": "^1.3.5", "ahooks": "^3.8.4", "antd": "^5.24.4", "array-move": "^4.0.0", "axios": "^1.8.4", "dayjs": "^1.11.13", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "file-saver": "^2.0.5", "geojson": "^0.5.0", "github-markdown-css": "^5.8.1", "history": "^5.3.0", "immer": "^10.1.1", "less": "^4.2.2", "less-loader": "^12.2.0", "lodash": "^4.17.21", "markdown-it": "^14.1.0", "nanoid": "^5.1.5", "query-string": "^9.1.1", "react": "^19.0.0", "react-custom-scrollbars": "^4.2.1", "react-dom": "^19.0.0", "react-grid-layout": "^1.5.1", "react-if": "^4.1.6", "react-infinite-scroll-component": "^6.1.0", "react-json-view": "^1.21.3", "react-router": "^7.4.0", "react-router-dom": "^7.4.0", "tailwindcss": "^4.0.15", "unstated-next": "^1.1.0", "vite-auto-i18n-plugin": "^1.0.26", "zustand": "^5.0.3"}, "devDependencies": {"@antfu/eslint-config": "^4.11.0", "@eslint-react/eslint-plugin": "^1.52.2", "@eslint/js": "^9.21.0", "@types/file-saver": "^2.0.7", "@types/lodash": "^4.17.16", "@types/node": "^22.5.4", "@types/react": "^19.0.10", "@types/react-custom-scrollbars": "^4.0.13", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react-swc": "^3.8.0", "eslint": "^9.23.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}, "pnpm": {"ignoredBuiltDependencies": ["@swc/core", "esbuild"], "onlyBuiltDependencies": ["@swc/core"]}}